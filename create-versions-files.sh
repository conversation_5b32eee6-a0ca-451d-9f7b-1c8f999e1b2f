#!/bin/bash

if [ -z ${CI+x} ]; then
  workdir=$PWD
  projectName=${workdir##*/}
  propertiesFile=$workdir/$projectName.properties
  if [ ! -f "$propertiesFile" ]; then
    if [ "$#" -ne 1 ]; then
      echo "Must give path to the project folder"
      exit 1
    fi
    workdir=$1
    projectName=$(basename $workdir)
    propertiesFile=$workdir/$projectName.properties
    if [ ! -f "$propertiesFile" ]; then
      echo "Cannot find project .properties file '$propertiesFile'"
      exit 2
    fi

    workdir=$(dirname $propertiesFile)
  fi
else
  workdir=$CI_PROJECT_DIR
  projectName=${CI_PROJECT_NAME}
  propertiesFile=$CI_PROJECT_DIR/${CI_PROJECT_NAME}.properties
fi

if [ -z ${NO_VERSION_SUFFIX+x} ]; then
  versionSuffixFile=$workdir/_buildVersionSuffix
  if [ ! -f "$versionSuffixFile" ]; then
    if [ -f $workdir/set-version-suffix.sh ]; then
      $workdir/set-version-suffix.sh $workdir
    fi
    if [ ! -f "$versionSuffixFile" ]; then
      currentScriptFolder=$(dirname $0)
      if [ -f $currentScriptFolder/set-version-suffix.sh ]; then
        $currentScriptFolder/set-version-suffix.sh $workdir
      fi
    fi
  fi
  if [ ! -f "$versionSuffixFile" ]; then
    echo "Cannot find version suffix file '$versionSuffixFile'"
    exit 3
  fi
  versionSuffix=$(cat $versionSuffixFile);
else
  versionSuffix=""
fi

outputFile=$workdir/_versions

line=$(cat $propertiesFile | grep project.code)
if [ -z "$line" ]; then
  key=$projectName
else
  IFS='=' read useless key <<< $line
fi
line=$(cat $propertiesFile | grep ^version.prefix=)
IFS='=' read useless versionPrefix <<< $line
echo "$versionPrefix" > _buildVersionPrefix

if [ -z ${NO_VERSION_SUFFIX+x} ]; then
  version=${versionPrefix}-${versionSuffix}
else
  version=${versionPrefix}
fi

echo "$version" > _buildVersion
echo "$key.version=$version" > $outputFile

versionProperties=$(cat $propertiesFile | grep "\.*version=" || true)
for versionProperty in $versionProperties
do
  IFS='=' read key versionPrefix <<< $versionProperty
  if [[ $versionPrefix == *-* ]]; then
    dependencyVersion=$versionPrefix
  else
    if [[ $key != *-version ]] && [[ $versionSuffix != master-* ]] && [[ $versionSuffix != rc ]] && [[ $versionSuffix != rc-* ]] && [[ $versionSuffix != stable ]] && [[ $versionSuffix != stable-* ]]; then
      dependencySuffix="master"
    else
      dependencySuffix=$versionSuffix
    fi
    dependencyVersion="${versionPrefix}-${dependencySuffix}"
  fi

  versionKey=${key%.prefix}
  echo "$versionKey=$dependencyVersion" >> $outputFile
done
