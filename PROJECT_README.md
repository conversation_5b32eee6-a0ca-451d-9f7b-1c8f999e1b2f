# Planify - Outil de Planification Multi-Sprints

## Description

Planify est un outil interne développé pour améliorer la planification multi-sprints dans Azure DevOps Server. Il offre une interface calendaire dédiée permettant de visualiser et gérer les Features, User Stories et Tasks de manière intuitive et ergonomique.

L'application propose une vue timeline moderne avec une colonne Features fixe et un calendrier scrollable horizontalement, facilitant la planification et le suivi des éléments de travail sur plusieurs sprints.

## Architecture

### Structure du projet
```
Planify/
├── frontend/          # Application Angular (SPA)
│   ├── src/
│   │   ├── app/
│   │   │   ├── components/
│   │   │   │   └── feature-timeline-calendar/  # Composant principal
│   │   │   ├── interfaces/                     # Types TypeScript
│   │   │   ├── services/                       # Services Angular
│   │   │   └── directives/                     # Directives personnalisées
│   │   └── styles/                             # Styles globaux SCSS
├── backend/           # API Python Flask
│   └── src/
│       └── app.py                              # API principale
└── docs/              # Documentation technique
```

### Technologies utilisées

#### Frontend
- **Angular 19** - Framework principal
- **TypeScript 5.7** - Langage de développement
- **SCSS** - Préprocesseur CSS avec variables Fluent Design
- **RxJS** - Gestion des flux de données asynchrones

#### Backend
- **Python 3.x** - Langage serveur
- **Flask** - Framework web léger
- **Flask-CORS** - Gestion des CORS
- **Requests** - Client HTTP pour Azure DevOps API

#### Infrastructure
- **Docker & Docker Compose** - Conteneurisation
- **GitLab CI/CD** - Intégration continue
- **Azure DevOps Server** - Source de données

## Installation et démarrage

### Prérequis
- Node.js 18+ et npm
- Python 3.8+
- Docker et Docker Compose (optionnel)
- Accès à Azure DevOps Server

### Installation locale

#### Frontend
```bash
cd frontend
npm install
npm start
# L'application sera disponible sur http://localhost:4200
```

#### Backend
```bash
cd backend
pip install -r requirements.txt

# Configuration des variables d'environnement
cp .env.example .env
# Éditer .env avec vos paramètres Azure DevOps

python src/app.py
# L'API sera disponible sur http://localhost:5000
```

### Configuration Azure DevOps

Créer un fichier `.env` dans le dossier `backend/` :
```env
AZURE_DEVOPS_SERVER_URL=https://your-server.com
AZURE_DEVOPS_ORG=YourOrganization
AZURE_DEVOPS_PROJECT=YourProject
AZURE_DEVOPS_TEAM=YourTeam
AZURE_DEVOPS_PAT=your-personal-access-token
SPRINT_PATH_PREFIX=YourProject\Sprint
```

### Déploiement avec Docker

```bash
# À la racine du projet
docker-compose up -d
```

## Fonctionnalités principales

### Interface timeline
- **Colonne Features fixe** : Affichage permanent des Features avec ID, titre et état
- **Timeline scrollable** : Calendrier horizontal avec jours ouvrés uniquement (lundi-vendredi)
- **User Stories visuelles** : Barres colorées représentant la durée et l'assignation
- **Scroll synchronisé** : En-têtes et contenu alignés en permanence

### Gestion des données
- **Sélection de sprint** : Dropdown pour choisir l'itération active
- **Couleurs par Feature** : Code couleur unique par Feature appliqué aux User Stories
- **Informations détaillées** : Tooltips avec titre et assignation au survol
- **Gestion des chevauchements** : Lignes multiples pour éviter les conflits visuels

### Types de Work Items supportés
- **Features** : Éléments principaux de planification
- **User Stories** : Éléments de développement avec dates et assignation
- **Tasks** : Tâches techniques liées aux User Stories

## Développement

### Structure des composants

#### FeatureTimelineCalendarComponent
Composant principal gérant l'affichage timeline :
- Gestion des données de sprint
- Calcul des plages de dates
- Rendu des Features et User Stories
- Synchronisation des scrolls

#### Services
- **PlanifyDataService** : Communication avec l'API backend
- Gestion des appels HTTP vers Azure DevOps
- Cache et transformation des données

#### Interfaces TypeScript
- **WorkItem** : Structure des éléments de travail Azure DevOps
- **Sprint** : Informations d'itération
- **FeatureTimeline** : Types spécifiques à la timeline

### Standards de code
- **OnPush Change Detection** : Optimisation des performances
- **Standalone Components** : Architecture Angular moderne
- **SCSS avec variables** : Design system cohérent
- **TypeScript strict** : Typage fort et sécurité

### Tests
```bash
# Frontend
cd frontend
npm test

# Backend
cd backend
python -m pytest
```

## API Backend

### Endpoints principaux

#### GET /api/sprints
Récupère la liste des sprints disponibles
```json
[
  {
    "id": "sprint-id",
    "name": "Sprint 1",
    "path": "Project\\Sprint 1",
    "attributes": {
      "startDate": "2024-01-01T00:00:00Z",
      "finishDate": "2024-01-14T23:59:59Z",
      "timeFrame": "current"
    }
  }
]
```

#### GET /api/sprints/{iteration_path}/features
Récupère les Features et User Stories d'un sprint
```json
[
  {
    "id": 123,
    "fields": {
      "System.Id": 123,
      "System.Title": "Feature Title",
      "System.WorkItemType": "Feature",
      "System.State": "Active",
      "Microsoft.VSTS.Scheduling.StartDate": "2024-01-01",
      "Microsoft.VSTS.Scheduling.FinishDate": "2024-01-10"
    }
  }
]
```

### Mode test
L'API fonctionne en mode test sans PAT Azure DevOps configuré, utilisant des données de démonstration.

## 🔧 Configuration

### Variables d'environnement Frontend
```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiBaseUrl: 'http://localhost:5000/api',
  debug: true
};
```

### Personnalisation des couleurs
Les couleurs suivent le Microsoft Fluent Design System :
- Bleu Microsoft (#0078d4)
- Vert (#107c10)
- Rouge (#d13438)
- Violet (#8764b8)
- Orange (#ca5010)

## Performance

### Optimisations implémentées
- **OnPush Change Detection** : Réduction des cycles de détection
- **Lazy Loading** : Chargement à la demande des composants
- **Scroll virtuel** : Gestion efficace des grandes listes
- **Debounce** : Limitation des appels API

### Métriques
- Temps de chargement initial : < 2s
- Scroll fluide jusqu'à 100+ User Stories
- Mémoire optimisée avec cleanup automatique

## Licence

Projet interne ITESOFT - Tous droits réservés