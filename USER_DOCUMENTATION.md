# Planify - Guide utilisateur

## 🎯 Introduction

Planify est votre outil de planification visuelle pour Azure DevOps Server. Il vous permet de visualiser et organiser vos Features, User Stories et Tasks dans une interface calendaire intuitive et moderne.

Cette documentation vous guide dans l'utilisation quotidienne de Planify pour optimiser votre planification multi-sprints.

## 🚀 Démarrage rapide

### Accès à l'application
1. Ouvrez votre navigateur web
2. Accédez à l'URL Planify de votre organisation
3. L'interface principale s'affiche avec le message de bienvenue

### Premier pas
1. **Sélectionnez un sprint** dans le menu déroulant "Itération" en haut à droite
2. **Attendez le chargement** des données (indicateur de progression visible)
3. **Explorez la timeline** qui s'affiche automatiquement

## 📅 Interface Principale

### Vue d'ensemble
L'interface Planify se compose de trois zones principales :

```
┌─────────────────────────────────────────────────────────────┐
│  Planify                                    [Itération ▼]   │ ← En-tête
├─────────────────────────────────────────────────────────────┤
│ Features    │  Lun │ Mar │ Mer │ Jeu │ Ven │ Lun │ Mar │... │ ← Timeline
│             │   1  │  2  │  3  │  4  │  5  │  8  │  9  │    │
├─────────────┼──────┼─────┼─────┼─────┼─────┼─────┼─────┼────┤
│ Feature A   │      │ ████████████████████ │     │     │    │ ← User Stories
│ #123        │      │ User Story 1         │     │     │    │
│ Active      │      │ Affectation: John    │     │     │    │
├─────────────┼──────┼─────┼─────┼─────┼─────┼─────┼─────┼────┤
│ Feature B   │ ██████████████ │           │     │     │    │
│ #124        │ User Story 2   │           │     │     │    │
│ New         │ Affectation: Marie         │     │     │    │
└─────────────┴──────┴─────┴─────┴─────┴─────┴─────┴─────┴────┘
```

### Zone en-tête
- **Titre Planify** : Identification de l'application
- **Sélecteur d'Itération** : Menu déroulant pour choisir le sprint actif
- **Indicateurs d'État** : Messages de chargement ou d'erreur

### Zone features (colonne fixe)
- **Titre de la Feature** : Nom complet de la Feature
- **ID** : Numéro d'identification Azure DevOps (#123)
- **État** : Statut actuel (Active, New, Resolved, etc.)
- **Couleur** : Code couleur unique par Feature

### Zone timeline (scrollable)
- **En-têtes de jours** : Jours ouvrés uniquement (Lundi à Vendredi)
- **Numéros de jours** : Date du mois
- **Barres User Stories** : Représentation visuelle des User Stories
- **Indicateur "Aujourd'hui"** : Mise en évidence du jour actuel

## 🎨 Système de couleurs

### Couleurs par feature
Chaque feature possède une couleur unique qui s'applique à toutes ses User Stories :

- 🔵 **Bleu Microsoft** (#0078d4) - Feature principale
- 🟢 **Vert** (#107c10) - Feature secondaire
- 🔴 **Rouge** (#d13438) - Feature critique
- 🟣 **Violet** (#8764b8) - Feature expérimentale
- 🟠 **Orange** (#ca5010) - Feature maintenance
- 🔷 **Teal** (#038387) - Feature infrastructure
- 🟪 **Lavande** (#8e8cd8) - Feature design
- 🔵 **Turquoise** (#00b7c3) - Feature test
- 🟡 **Vert lime** (#bad80a) - Feature documentation

### Signification visuelle
- **Longueur de la barre** : Durée de la User Story
- **Position** : Dates de début et fin
- **Couleur** : Appartenance à une Feature
- **Hauteur** : Ligne d'affichage (évite les chevauchements)

## 🖱️ Navigation et interactions

### Navigation horizontale
- **Scroll horizontal** : Utilisez la molette de la souris ou la barre de défilement
- **Scroll synchronisé** : Les en-têtes et le contenu bougent ensemble
- **Zoom** : Utilisez Ctrl + molette pour ajuster l'affichage

### Navigation verticale
- **Scroll vertical** : Parcourez les Features quand elles sont nombreuses
- **Colonne fixe** : La colonne Features reste visible pendant le scroll horizontal

### Informations détaillées
- **Survol** : Passez la souris sur une User Story pour voir les détails
- **Tooltip** : Affiche le titre complet et l'assignation

## 📊 Gestion des sprints

### Sélection d'un sprint
1. **Cliquez** sur le menu déroulant "Itération"
2. **Choisissez** le sprint désiré dans la liste
3. **Attendez** le chargement automatique des données
4. **Visualisez** les Features et User Stories du sprint

### Types de sprints affichés
- **Sprints actuels** : En cours d'exécution
- **Sprints futurs** : Planifiés mais non démarrés
- **Sprints récents** : Terminés récemment (selon configuration)

### Informations de sprint
- **Nom du sprint** : Affiché dans le sélecteur
- **Dates** : Période couverte par la timeline
- **Statut** : Indiqué par la couleur et l'état

## 📋 Types d'éléments

### Features
- **Rôle** : Éléments principaux de planification
- **Affichage** : Une ligne par Feature dans la colonne fixe
- **Contenu** : Titre, ID, état et couleur distinctive
- **Relation** : Contient plusieurs User Stories

### User stories
- **Rôle** : Éléments de développement détaillés
- **Affichage** : Barres horizontales colorées sur la timeline
- **Informations** : Titre, assignation, dates de début/fin
- **Positionnement** : Alignées avec leur Feature parente

### Tasks
- **Rôle** : Tâches techniques liées aux User Stories
- **Affichage** : Intégrées dans la planification des User Stories
- **Gestion** : Suivent les mêmes règles de couleur et positionnement

## 🔍 Fonctionnalités avancées

### Gestion des chevauchements
- **Détection automatique** : Planify détecte les User Stories qui se chevauchent
- **Lignes multiples** : Affichage sur plusieurs lignes pour éviter la confusion
- **Hauteur dynamique** : Les Features s'adaptent au nombre d'User Stories

### Affichage des assignations
- **Format** : "Affectation: [Nom de la personne]"
- **Position** : Deuxième ligne de chaque User Story
- **Visibilité** : Toujours visible sur les barres suffisamment larges

### Indicateur jour actuel
- **Marquage** : Le jour actuel est mis en évidence
- **Navigation** : Facilite l'orientation temporelle
- **Mise à jour** : Automatique chaque jour

## ⚠️ Résolution de problèmes

### Problèmes courants

#### "Aucune donnée affichée"
- **Vérifiez** que vous avez sélectionné un sprint
- **Confirmez** que le sprint contient des Features/User Stories
- **Actualisez** la page si nécessaire

#### "Chargement infini"
- **Patientez** quelques secondes supplémentaires
- **Vérifiez** votre connexion réseau
- **Contactez** l'administrateur si le problème persiste

#### "User Stories mal alignées"
- **Actualisez** la page pour resynchroniser l'affichage
- **Vérifiez** que les dates sont correctement définies dans Azure DevOps
- **Signalez** le problème si il persiste

### Messages d'erreur
- **"Erreur de chargement"** : Problème de connexion à Azure DevOps
- **"Sprint introuvable"** : Le sprint sélectionné n'existe plus
- **"Données corrompues"** : Problème de format des données

## 💡 Conseils d'utilisation

### Optimisation de l'affichage
- **Utilisez un écran large** pour une meilleure visibilité
- **Ajustez le zoom** selon vos préférences
- **Organisez vos Features** par priorité dans Azure DevOps

### Planification efficace
- **Définissez des dates réalistes** pour vos User Stories
- **Assignez clairement** chaque User Story
- **Utilisez les couleurs** pour identifier rapidement les Features

### Collaboration d'équipe
- **Partagez l'URL** avec votre équipe
- **Utilisez la vue** lors des réunions de planification
- **Mettez à jour** régulièrement Azure DevOps pour refléter les changements

## 📞 Support et assistance

### Ressources disponibles
- **Documentation technique** : PROJECT_README.md
- **Équipe de développement** : Contact via GitLab
- **Formation** : Sessions disponibles sur demande

### Signalement de problèmes
1. **Décrivez** le problème rencontré
2. **Incluez** les étapes de reproduction
3. **Précisez** votre navigateur et version
4. **Joignez** une capture d'écran si pertinent

---

*Cette documentation est mise à jour régulièrement. Consultez la version la plus récente sur le repository du projet.*
