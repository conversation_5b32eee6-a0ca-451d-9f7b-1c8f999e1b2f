#!/bin/bash

if [ -z ${CI+x} ]; then
  workdir="$PWD"
else
  workdir=$CI_PROJECT_DIR
fi

function printHelp
{
  echo "Usage: $0 [-p <path_to_project>] [-f <properties_file>]"
  echo "  If no path to project is provided, current folder is used"
}

while getopts 'p:f:h' opt; do
  case "$opt" in
    p)
      workdir=$OPTARG
      ;;
    f)
      propertiesFile="$OPTARG"
      ;;
    h)
      printHelp
      exit 0
      ;;
    :)
      printHelp
      exit 1
      ;;
    ?)
      printHelp
      exit 1
      ;;
  esac
done

versionsFile=$workdir/_versions
if [ ! -f "$versionsFile" ]; then
  echo "Cannot find versions file '$versionsFile'"
  exit 2
fi

function replaceUsingKeyValuePair
{
  keyValuePair=$1
  keyPrefix=$2
  local destinationFile=$3

  key=$(echo "$line" | awk -v RS="\n\n" -v FS="=" '{print $1}')
  totalLength=${#keyValuePair}
  keyLength=${#key}
  valueLength=$(($totalLength-$keyLength))
  value=${keyValuePair:$keyLength + 1:$valueLength}
  if [[ $value == */* && $value == *#* ]]; then
    echo "Env variable named '$key' contains / and #, cannot perform replacement for that env variable"
    return
  fi

  if [[ $value == */* ]]; then
    sep="#"
  else
    sep="/"
  fi
  sed -i "s${sep}@${keyPrefix}${key}@${sep}${value}${sep}g" $destinationFile
}

versions=$(cat $versionsFile | grep .version || true)

templateFiles=$(find $workdir -name "*.tpl")
for templateFile in $templateFiles
do
  destinationFile=${templateFile%.tpl}
  echo "Replacing patterns in template file '$templateFile', generating '$destinationFile'"
  cp $templateFile $destinationFile

  for line in $versions
  do
    IFS='=' read key version <<< $line
    sed -i "s/@$key@/$version/g" $destinationFile
  done

  while read -r -d $'\0' line; do
    replaceUsingKeyValuePair "$line" "env." "$destinationFile"
  done < <(env -0)

  if [[ ! -z $propertiesFile ]]; then
    while read -r line; do
      replaceUsingKeyValuePair "$line" "" "$destinationFile"
    done < "$propertiesFile"
  fi
done
