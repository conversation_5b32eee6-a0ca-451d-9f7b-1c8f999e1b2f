# Planify

Planify est un outil interne destiné à améliorer la planification multi-sprints dans Azure DevOps Server, avec une interface calendaire dédiée.

## Structure du projet

- **frontend/** : application Angular (SPA)
- **backend/** : API Python Flask, interfaçant Azure DevOps via son API

## Technologies

- Angular 17
- Python + Flask
- Docker & Docker Compose
- GitLab CI/CD

## Lancement

À venir.
