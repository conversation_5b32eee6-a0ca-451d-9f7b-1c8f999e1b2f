# ============ Node / Angular / Frontend ============
node_modules/
dist/
.angular/
tmp/
out-tsc/
bazel-out/
npm-debug.log
yarn-error.log
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings
frontend/node_modules/
frontend/dist/
frontend/.angular/

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/
.history/

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# ============ Python / Backend ============
__pycache__/
*.pyc
env/
.env/
venv/
.venv/
backend/.venv/

# ============ General ============
.env
*.log
.DS_Store
Thumbs.db
