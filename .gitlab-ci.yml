include:
  - project: "development/common/release-management/ci/gitlab-build"
    ref: "master"
    file: "common.yml"

default:
  tags:
    - docker

stages:
  - check_versions
  - define_version
  - commit_version
  - prepare_env
  - test
  - build_web_app
  - build_backend
  - build_docker
  - tag
  - publish

variables:
  BACKEND_PATH: $CI_PROJECT_DIR/backend
  BACKEND_DOCKER_PATH: $BACKEND_PATH/docker
  BACKEND_TARGET_PATH: $BACKEND_PATH/target
  BACKEND_IMAGE_NAME: planify-backend
  WEB_APP_PATH: $CI_PROJECT_DIR/frontend
  WEB_APP_DOCKER_PATH: $WEB_APP_PATH/docker
  WEB_APP_TARGET_PATH: $WEB_APP_PATH/target
  WEB_APP_IMAGE_NAME: planify-web-app

.custom prepare:
  before_script: [ ]

.custom update version:
  script: [ ]

prepare environment:
  extends: .prepare environment

.default-node-builder:
  image:
    name: itesoft/tool/node20-builder:24.10.1

.default-docker-builder:
  extends: .dind
  image:
    name: itesoft/tool/docker-builder:25.6.1

build-front:
  stage: build_web_app
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm install --no-audit
    - npm run build
  artifacts:
    name: planify-web-app
    expire_in: 2 days
    paths:
      - $WEB_APP_PATH/dist
  rules:
    - !reference [ .building_rules, rules ]

build-docker-frontend:
  stage: build_docker
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - IMAGE_FULL_NAME=${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
    - echo $VERSION
    - echo $WEB_APP_IMAGE_NAME
    - echo $IMAGE_FULL_NAME
    - mkdir -p $WEB_APP_TARGET_PATH/root/usr/share/nginx/html
    - cp $WEB_APP_DOCKER_PATH/Dockerfile $WEB_APP_TARGET_PATH/
    - cp -r $WEB_APP_DOCKER_PATH/root/* $WEB_APP_TARGET_PATH/root
    - cp -r $WEB_APP_PATH/dist/frontend/browser/* $WEB_APP_TARGET_PATH/root/usr/share/nginx/html
    - docker build -t $IMAGE_FULL_NAME --build-arg="VERSION=${VERSION}" --build-arg CI_COMMIT_SHA --build-arg CI_JOB_STARTED_AT $WEB_APP_TARGET_PATH
    - docker save -o $WEB_APP_TARGET_PATH/${WEB_APP_IMAGE_NAME}-${VERSION}.tar $IMAGE_FULL_NAME
    - docker rmi $IMAGE_FULL_NAME
  artifacts:
    name: planify-web-app-image
    when: on_success
    expire_in: 2 days
    paths:
      - $WEB_APP_TARGET_PATH/*.tar
  rules:
    - !reference [ .building_rules, rules ]

build-docker-backend:
  stage: build_docker
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - IMAGE_FULL_NAME=${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
    - echo $VERSION
    - echo $BACKEND_IMAGE_NAME
    - echo $IMAGE_FULL_NAME
    - mkdir -p $BACKEND_TARGET_PATH
    - cp -r $BACKEND_PATH/requirements.txt $BACKEND_TARGET_PATH/
    - cp -r $BACKEND_PATH/src $BACKEND_TARGET_PATH/
    - cp -r $BACKEND_DOCKER_PATH/Dockerfile $BACKEND_TARGET_PATH/
    - docker build -t $IMAGE_FULL_NAME --build-arg="VERSION=${VERSION}" --build-arg CI_COMMIT_SHA --build-arg CI_JOB_STARTED_AT $BACKEND_TARGET_PATH
    - docker save -o $BACKEND_TARGET_PATH/${BACKEND_IMAGE_NAME}-${VERSION}.tar $IMAGE_FULL_NAME
    - docker rmi $IMAGE_FULL_NAME
  artifacts:
    name: planify-backend-image
    when: on_success
    expire_in: 2 days
    paths:
      - $BACKEND_TARGET_PATH/*.tar
  rules:
    - !reference [ .building_rules, rules ]

publish-docker-web-app:
  stage: publish
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - echo $VERSION
    - echo $WEB_APP_IMAGE_NAME
    - docker load -i $WEB_APP_TARGET_PATH/${WEB_APP_IMAGE_NAME}-${VERSION}.tar
    - docker login -u $DOCKER_USERNAME -p "$DOCKER_PASSWORD" $DOCKER_SERVER
    - docker push ${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
    - docker rmi ${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
  rules:
    - !reference [ .building_rules, rules ]

publish-docker-backend:
  stage: publish
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - echo $VERSION
    - echo $BACKEND_IMAGE_NAME
    - docker load -i $BACKEND_TARGET_PATH/${BACKEND_IMAGE_NAME}-${VERSION}.tar
    - docker login -u $DOCKER_USERNAME -p "$DOCKER_PASSWORD" $DOCKER_SERVER
    - docker push ${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
    - docker rmi ${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
  rules:
    - !reference [ .building_rules, rules ]