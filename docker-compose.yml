services:
  planify-web-app:
    image: ${REGISTRY:-}itesoft/slfi/qa/planify-web-app:${PLANIFY_TAG}
    container_name: planify-web-app
    env_file:
      - env/web-app.env
    ports:
      - 4200:80
    networks:
      - planify

  planify-backend:
    image: ${REGISTRY:-}itesoft/slfi/qa/planify-backend:${PLANIFY_TAG}
    container_name: planify-backend
    env_file:
      - env/backend.env
    ports:
      - 5000:5000
    networks:
      - planify

networks:
  planify:
    driver: bridge