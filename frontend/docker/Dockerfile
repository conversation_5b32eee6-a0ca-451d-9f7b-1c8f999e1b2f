#renovate: datasource=docker depName=itesoft/external/nginx1
ARG IMAGE_VERSION=25.6.4

FROM itesoft/external/nginx1:${IMAGE_VERSION}

ARG IMAGE_VERSION
ARG VERSION
ARG CI_COMMIT_SHA
ARG CI_JOB_STARTED_AT

LABEL org.opencontainers.image.authors="<EMAIL>" \
      org.opencontainers.image.title="Itesoft's planify web app" \
      org.opencontainers.image.description="Itesoft's planify web app based on nginx1" \
      org.opencontainers.image.base.name="itesoft/external/nginx1:${IMAGE_VERSION}" \
      org.opencontainers.image.vendor="ITESOFT" \
      org.opencontainers.image.version="$VERSION" \
      org.opencontainers.image.revision="$CI_COMMIT_SHA" \
      org.opencontainers.image.create="$CI_JOB_STARTED_AT"

COPY --chown=${IT_USERNAME}:${IT_GROUPNAME} root/ /

RUN find /start.d/ -type f -iname "*.sh" -exec chmod +x '{}' \; -exec sed -i -e 's/\r$//' '{}' \;
