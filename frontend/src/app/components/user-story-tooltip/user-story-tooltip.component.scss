// Import des variables globales
@use '../../../styles/variables' as vars;

.tooltip-content {
  background: vars.$background-white;
  border: vars.$border-width-thin solid #d1d1d1;
  border-radius: vars.$border-radius-lg;
  box-shadow: vars.$shadow-lg;
  padding: vars.$spacing-md;
  min-width: 280px;
  max-width: 350px;
  font-family: vars.$font-family;
  font-size: 13px;
  line-height: 1.4;
  z-index: vars.$z-index-tooltip;
  position: relative;

  // Flèche du tooltip
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #d1d1d1;
  }

  &::after {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #ffffff;
  }
}

.tooltip-header {
  border-bottom: 1px solid #e1e1e1;
  padding-bottom: 8px;
  margin-bottom: 10px;

  .tooltip-title {
    font-weight: 600;
    font-size: 14px;
    color: #323130;
    margin-bottom: 2px;
    line-height: 1.3;
  }

  .tooltip-id {
    font-size: 12px;
    color: #605e5c;
    font-weight: 500;
  }
}

.tooltip-body {
  .tooltip-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
    gap: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .tooltip-label {
      font-weight: 500;
      color: #605e5c;
      min-width: 85px;
      flex-shrink: 0;
    }

    .tooltip-value {
      color: #323130;
      text-align: right;
      flex: 1;
      word-break: break-word;

      &.state {
        font-weight: 500;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.state-new {
          background: vars.$state-new-bg;
          color: vars.$state-new-color;
          border: vars.$border-width-thin solid vars.$state-new-border;
        }

        &.state-active {
          background: vars.$state-active-bg;
          color: vars.$state-active-color;
          border: vars.$border-width-thin solid vars.$state-active-border;
        }

        &.state-closed {
          background: vars.$state-closed-bg;
          color: vars.$state-closed-color;
          border: vars.$border-width-thin solid vars.$state-closed-border;
        }

        &.state-resolved {
          background: vars.$state-resolved-bg;
          color: vars.$state-resolved-color;
          border: vars.$border-width-thin solid vars.$state-resolved-border;
        }

        &.state-default {
          background: vars.$state-default-bg;
          color: vars.$state-default-color;
          border: vars.$border-width-thin solid vars.$state-default-border;
        }
      }
    }
  }
}

// Animation d'apparition
.tooltip-content {
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Style pour tooltip affiché en bas
:host-context(.tooltip-bottom) .tooltip-content {
  &::before {
    top: auto;
    bottom: -6px;
    border-bottom: none;
    border-top: 6px solid #d1d1d1;
  }

  &::after {
    top: auto;
    bottom: -5px;
    border-bottom: none;
    border-top: 5px solid #ffffff;
  }
}
