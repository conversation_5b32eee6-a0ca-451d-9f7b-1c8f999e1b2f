import { Component, Input } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { WorkItem } from '../../interfaces/work-item';

@Component({
  selector: 'app-user-story-tooltip',
  standalone: true,
  imports: [CommonModule, DatePipe],
  template: `
    <div class="tooltip-content" *ngIf="userStory">
      <div class="tooltip-header">
        <div class="tooltip-title">{{ userStory.fields['System.Title'] }}</div>
        <div class="tooltip-id">#{{ userStory.id }}</div>
      </div>

      <div class="tooltip-body">
        <div class="tooltip-row">
          <span class="tooltip-label">État :</span>
          <span class="tooltip-value state" [class]="getStateClass()">
            {{ userStory.fields['System.State'] }}
          </span>
        </div>

        <div class="tooltip-row">
          <span class="tooltip-label">Affectation :</span>
          <span class="tooltip-value">
            {{ userStory.fields['System.AssignedTo']?.displayName || 'Non assigné' }}
          </span>
        </div>

        <div class="tooltip-row">
          <span class="tooltip-label">Date de début :</span>
          <span class="tooltip-value">
            {{ userStory.fields['Microsoft.VSTS.Scheduling.StartDate'] | date:'dd/MM/yyyy' }}
          </span>
        </div>

        <div class="tooltip-row">
          <span class="tooltip-label">Date de fin :</span>
          <span class="tooltip-value">
            {{ userStory.fields['Microsoft.VSTS.Scheduling.FinishDate'] | date:'dd/MM/yyyy' }}
          </span>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./user-story-tooltip.component.scss']
})
export class UserStoryTooltipComponent {
  @Input() userStory: WorkItem | null = null;

  getStateClass(): string {
    const state = this.userStory?.fields['System.State'];
    switch (state) {
      case 'New': return 'state-new';
      case 'Active': return 'state-active';
      case 'Closed': return 'state-closed';
      case 'Resolved': return 'state-resolved';
      default: return 'state-default';
    }
  }
}
