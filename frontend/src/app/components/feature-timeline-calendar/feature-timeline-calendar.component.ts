// Planify/frontend/src/app/components/feature-timeline-calendar/feature-timeline-calendar.component.ts
import { Component, Input, OnChanges, SimpleChanges, AfterViewInit, ViewChild, ElementRef, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorkItem } from '../../interfaces/work-item';
import { Sprint } from '../../interfaces/sprint';
import { TimelineDay, FeatureRow, UserStoryBar, TimelineConfig } from '../../interfaces/feature-timeline';
import { UserStoryTooltipDirective } from '../../directives/user-story-tooltip.directive';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-feature-timeline-calendar',
  standalone: true,
  imports: [CommonModule, UserStoryTooltipDirective],

  templateUrl: './feature-timeline-calendar.component.html',
  styleUrls: ['./feature-timeline-calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FeatureTimelineCalendarComponent implements OnChanges, AfterViewInit {
  @Input() sprint: Sprint | null = null;
  @Input() workItems: WorkItem[] = [];

  @ViewChild('unifiedGrid', { static: false }) unifiedGrid!: ElementRef;

  private readonly DEBUG_MODE = environment.debug;

  protected timelineDays: TimelineDay[] = [];
  protected featureRows: FeatureRow[] = [];
  protected config: TimelineConfig = {
    dayWidth: 120, // Synchronisé avec $day-width dans _variables.scss
    rowHeight: 60,
    featureColumnWidth: 300, // Synchronisé avec $feature-width dans _variables.scss
    startDate: new Date(),
    endDate: new Date(),
    totalDays: 0
  };

  // Couleurs synchronisées avec _variables.scss
  private readonly FEATURE_COLORS = [
    '#0078d4', '#107c10', '#d13438', '#8764b8', '#ca5010',
    '#038387', '#8e8cd8', '#00b7c3', '#bad80a'
  ];

  private featureColorMap = new Map<number, string>();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['sprint'] || changes['workItems']) {
      this.buildTimeline();
    }
  }

  ngAfterViewInit(): void {
    this.updateGridConfiguration();
  }



  private buildTimeline(): void {
    if (!this.sprint) {
      this.timelineDays = [];
      this.featureRows = [];
      this.cdr.markForCheck();
      return;
    }

    // Always build timeline structure when sprint is available
    this.calculateTimelineRange();
    this.generateTimelineDays();
    this.buildFeatureRows();

    this.updateGridConfiguration();
    this.cdr.markForCheck();
  }

  private calculateTimelineRange(): void {
    const sprintStart = new Date(this.sprint!.attributes.startDate);
    const sprintEnd = new Date(this.sprint!.attributes.finishDate);

    let earliestDate = sprintStart;
    let latestDate = sprintEnd;

    // Only calculate work item dates if there are work items
    if (this.workItems.length > 0) {
      this.workItems.forEach(item => {
        if (item.fields['System.WorkItemType'] === 'User Story') {
          const startDate = this.getWorkItemStartDate(item);
          const endDate = this.getWorkItemEndDate(item);

          if (startDate < earliestDate) earliestDate = startDate;
          if (endDate > latestDate) latestDate = endDate;
        }
      });
    }

    this.config.startDate = new Date(sprintStart);
    this.config.startDate.setMonth(this.config.startDate.getMonth() - 1);

    if (earliestDate < this.config.startDate) {
      this.config.startDate = new Date(earliestDate);
      this.config.startDate.setDate(this.config.startDate.getDate() - 7);
    }

    this.config.endDate = new Date(sprintEnd);
    this.config.endDate.setMonth(this.config.endDate.getMonth() + 6);

    if (latestDate > this.config.endDate) {
      this.config.endDate = new Date(latestDate);
      this.config.endDate.setMonth(this.config.endDate.getMonth() + 2);
    }

    if (this.DEBUG_MODE) {
      console.log(`📅 Timeline: ${this.config.startDate.toDateString()} → ${this.config.endDate.toDateString()}`);
    }
  }

  private generateTimelineDays(): void {
    this.timelineDays = [];
    const currentDate = new Date(this.config.startDate);

    while (currentDate <= this.config.endDate) {
      const dayOfWeek = currentDate.getDay();

      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        this.timelineDays.push({
          date: new Date(currentDate),
          dayName: this.getDayName(currentDate),
          dayNumber: this.formatDate(currentDate),
          isToday: this.isToday(currentDate)
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    this.config.totalDays = this.timelineDays.length;
  }

  private buildFeatureRows(): void {
    const features = this.workItems.filter(item =>
      item.fields['System.WorkItemType'] === 'Feature'
    );

    const userStories = this.workItems.filter(item =>
      item.fields['System.WorkItemType'] === 'User Story'
    );

    // Debug minimal
    if (this.DEBUG_MODE) {
      console.log(`📊 ${features.length} Features, ${userStories.length} User Stories chargées`);
    }

    this.assignFeatureColors(features);

    // Créer les lignes pour les Features existantes
    this.featureRows = features.map(feature => ({
      feature,
      color: this.featureColorMap.get(feature.id) || this.FEATURE_COLORS[0],
      userStories: this.createUserStoryBars(userStories, feature)
    }));

    // Gérer les User Stories orphelines (sans parent)
    this.handleOrphanedUserStories(userStories);
  }

  private handleOrphanedUserStories(userStories: WorkItem[]): void {
    // Récupérer tous les IDs des Features existantes
    const existingFeatureIds = this.featureRows.map(row => String(row.feature.id));

    // Récupérer tous les IDs des User Stories déjà assignées à leurs Features parentes
    const assignedUserStoryIds = new Set<string>();
    this.featureRows.forEach(row => {
      row.userStories.forEach(us => {
        assignedUserStoryIds.add(String(us.userStory.id));
      });
    });

    // Filtrer les vraies User Stories orphelines (sans parent OU parent inexistant OU pas encore assignées)
    const orphanedUS = userStories.filter(us => {
      const userStoryId = String(us.id);

      // Si cette User Story est déjà assignée à une Feature, elle n'est pas orpheline
      if (assignedUserStoryIds.has(userStoryId)) {
        return false;
      }

      const parentId = us.fields['System.Parent'];
      if (!parentId) return true; // Pas de parent du tout

      // Vérifier si le parent existe dans les Features chargées
      return !existingFeatureIds.includes(String(parentId));
    });

    if (this.DEBUG_MODE && orphanedUS.length > 0) {
      console.log(`⚠️ ${orphanedUS.length} US orphelines (${assignedUserStoryIds.size} déjà assignées)`);
    }

    if (orphanedUS.length > 0) {
      // Créer une Feature virtuelle pour les User Stories orphelines
      const orphanedFeature: WorkItem = {
        id: -1,
        rev: 1,
        fields: {
          'System.Id': -1,
          'System.Title': 'User Stories sans Feature',
          'System.State': 'Active',
          'System.WorkItemType': 'Feature',
          'System.CreatedDate': new Date().toISOString()
        },
        url: ''
      };

      // Assigner une couleur à la Feature orpheline
      this.featureColorMap.set(-1, '#808080'); // Gris pour les orphelines

      // Créer les barres pour les User Stories orphelines
      const orphanedBars = orphanedUS.map(us => {
        const startDate = this.getWorkItemStartDate(us);
        const endDate = this.getWorkItemEndDate(us);
        const startDayIndex = this.findDayIndex(startDate);
        const durationDays = this.calculateWorkDaysBetween(startDate, endDate);

        return {
          userStory: us,
          startDate,
          endDate,
          startDayIndex: Math.max(0, startDayIndex),
          durationDays,
          assignedTo: us.fields['System.AssignedTo']?.displayName || 'Non assigné',
          title: us.fields['System.Title'],
          state: us.fields['System.State'] || 'Non défini',
          color: '#808080',
          row: 0
        };
      });

      this.calculateRowPositions(orphanedBars);

      // Ajouter la ligne des User Stories orphelines
      this.featureRows.push({
        feature: orphanedFeature,
        color: '#808080',
        userStories: orphanedBars
      });


    }
  }

  private assignFeatureColors(features: WorkItem[]): void {
    this.featureColorMap.clear();
    features.forEach((feature, index) => {
      const colorIndex = index % this.FEATURE_COLORS.length;
      this.featureColorMap.set(feature.id, this.FEATURE_COLORS[colorIndex]);
    });
  }

  private createUserStoryBars(userStories: WorkItem[], feature: WorkItem): UserStoryBar[] {
    // Filtrer les User Stories liées à cette Feature
    // Gestion robuste des types (string vs number) pour System.Parent
    const featureUserStories = userStories.filter(us => {
      const parentId = us.fields['System.Parent'];
      if (!parentId) return false;

      // Comparaison robuste : convertir les deux en string pour éviter les problèmes de type
      return String(parentId) === String(feature.id);
    });



    const userStoryBars = featureUserStories.map(us => {
      const startDate = this.getWorkItemStartDate(us);
      const endDate = this.getWorkItemEndDate(us);
      const startDayIndex = this.findDayIndex(startDate);
      const durationDays = this.calculateWorkDaysBetween(startDate, endDate);

      // Debug pour les User Stories hors timeline
      if (this.DEBUG_MODE && startDayIndex === -1) {
        console.warn(`❌ "${us.fields['System.Title']}" hors timeline`);
      }

      // Nettoyer le titre de l'User Story en supprimant les préfixes redondants
      const cleanedTitle = this.cleanUserStoryTitle(us.fields['System.Title'], feature.fields['System.Title']);

      return {
        userStory: us,
        startDate,
        endDate,
        startDayIndex: Math.max(0, startDayIndex),
        durationDays,
        assignedTo: us.fields['System.AssignedTo']?.displayName || 'Non assigné',
        title: cleanedTitle,
        state: us.fields['System.State'] || 'Non défini',
        color: this.featureColorMap.get(feature.id) || this.FEATURE_COLORS[0],
        row: 0
      };
    });

    this.calculateRowPositions(userStoryBars);

    return userStoryBars;
  }

  /**
   * Nettoie le titre d'une User Story en supprimant les préfixes redondants avec sa Feature parent
   * @param userStoryTitle Titre complet de l'User Story
   * @param featureTitle Titre de la Feature parent
   * @returns Titre nettoyé de l'User Story
   */
  private cleanUserStoryTitle(userStoryTitle: string, featureTitle: string): string {
    if (!userStoryTitle || !featureTitle) {
      return userStoryTitle;
    }

    // Extraire tous les préfixes entre crochets du titre de la Feature
    const featurePrefixes = this.extractBracketPrefixes(featureTitle);

    // Extraire tous les préfixes entre crochets du titre de l'User Story
    const userStoryPrefixes = this.extractBracketPrefixes(userStoryTitle);

    if (featurePrefixes.length === 0 || userStoryPrefixes.length === 0) {
      return userStoryTitle;
    }

    // Trouver le préfixe commun le plus long
    const commonPrefix = this.findCommonPrefix(featurePrefixes, userStoryPrefixes);

    if (commonPrefix) {
      // Supprimer le préfixe commun du titre de l'User Story
      const cleanedTitle = userStoryTitle.replace(commonPrefix, '').trim();
      return cleanedTitle || userStoryTitle; // Retourner le titre original si le nettoyage donne une chaîne vide
    }

    return userStoryTitle;
  }

  /**
   * Extrait tous les préfixes entre crochets d'un titre
   * @param title Titre à analyser
   * @returns Array des préfixes trouvés (avec les crochets)
   */
  private extractBracketPrefixes(title: string): string[] {
    const prefixes: string[] = [];
    const regex = /\[([^\]]+)\]/g;
    let match;

    while ((match = regex.exec(title)) !== null) {
      prefixes.push(match[0]); // Inclure les crochets
    }

    return prefixes;
  }

  /**
   * Trouve le préfixe commun le plus long entre les préfixes de Feature et d'User Story
   * @param featurePrefixes Préfixes de la Feature
   * @param userStoryPrefixes Préfixes de l'User Story
   * @returns Le préfixe commun le plus long ou null
   */
  private findCommonPrefix(featurePrefixes: string[], userStoryPrefixes: string[]): string | null {
    let longestCommon = '';

    for (const featurePrefix of featurePrefixes) {
      for (const userStoryPrefix of userStoryPrefixes) {
        if (featurePrefix === userStoryPrefix && featurePrefix.length > longestCommon.length) {
          longestCommon = featurePrefix;
        }
      }
    }

    return longestCommon || null;
  }

  private calculateRowPositions(userStoryBars: UserStoryBar[]): void {
    userStoryBars.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());

    const lanes: { endDayIndex: number }[] = [];

    userStoryBars.forEach(bar => {
      let laneIndex = lanes.findIndex(lane => lane.endDayIndex < bar.startDayIndex);

      if (laneIndex === -1) {
        laneIndex = lanes.length;
        lanes.push({ endDayIndex: bar.startDayIndex + bar.durationDays });
      } else {
        lanes[laneIndex].endDayIndex = bar.startDayIndex + bar.durationDays;
      }

      bar.row = laneIndex;
    });
  }

  private findDayIndex(date: Date): number {
    let index = this.timelineDays.findIndex(day =>
      day.date.toDateString() === date.toDateString()
    );

    if (index !== -1) {
      return index;
    }

    const targetDate = new Date(date);

    if (targetDate.getDay() === 0) {
      targetDate.setDate(targetDate.getDate() + 1);
    } else if (targetDate.getDay() === 6) {
      targetDate.setDate(targetDate.getDate() + 2);
    }

    index = this.timelineDays.findIndex(day =>
      day.date.toDateString() === targetDate.toDateString()
    );

    if (index !== -1) {
      return index;
    }

    let closestIndex = 0;
    let minDiff = Math.abs(this.timelineDays[0].date.getTime() - date.getTime());

    for (let i = 1; i < this.timelineDays.length; i++) {
      const diff = Math.abs(this.timelineDays[i].date.getTime() - date.getTime());
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = i;
      }
    }

    if (this.DEBUG_MODE) {
      console.warn(`Date ${date.toDateString()} not found in timeline, using closest day: ${this.timelineDays[closestIndex].date.toDateString()}`);
    }
    return closestIndex;
  }

  private calculateWorkDaysBetween(startDate: Date, endDate: Date): number {
    const normalizedStartDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const normalizedEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    const isSameDay = normalizedStartDate.getTime() === normalizedEndDate.getTime();

    if (isSameDay) {
      const dayOfWeek = normalizedStartDate.getDay();
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        return 1;
      } else {
        // Weekend handling - move to next Monday
        return 1;
      }
    }

    // Multi-day calculation
    let count = 0;
    const currentDate = new Date(normalizedStartDate);

    while (currentDate <= normalizedEndDate) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return Math.max(1, count);
  }

  /**
   * Parse Azure DevOps dates correctly (handles UTC format)
   */
  private parseAzureDevOpsDate(dateStr: string): Date {
    if (!dateStr) {
      return new Date();
    }

    // Handle UTC format with Z suffix
    if (dateStr.endsWith('Z')) {
      const match = dateStr.match(/^(\d{4})-(\d{2})-(\d{2})T/);
      if (match) {
        const [, year, month, day] = match;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
    }

    // Fallback for other formats
    const date = new Date(dateStr);
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  }

  private getWorkItemStartDate(workItem: WorkItem): Date {
    const startDateStr = workItem.fields['Microsoft.VSTS.Scheduling.StartDate'];
    if (startDateStr) {
      return this.parseAzureDevOpsDate(startDateStr);
    }

    const sprintStartDate = this.sprint!.attributes.startDate;
    return this.parseAzureDevOpsDate(sprintStartDate);
  }

  private getWorkItemEndDate(workItem: WorkItem): Date {
    const endDateStr = workItem.fields['Microsoft.VSTS.Scheduling.FinishDate'];
    if (endDateStr) {
      return this.parseAzureDevOpsDate(endDateStr);
    }

    // If no end date specified, default to same day as start date
    return this.getWorkItemStartDate(workItem);
  }

  private updateGridConfiguration(): void {
    if (this.unifiedGrid?.nativeElement) {
      const gridElement = this.unifiedGrid.nativeElement;
      gridElement.style.setProperty('--timeline-days-count', this.timelineDays.length.toString());
      this.updateGridRowHeights(gridElement);
    }
  }

  private updateGridRowHeights(gridElement: HTMLElement): void {
    // Calculer les hauteurs de toutes les lignes
    const headerHeight = 60; // Hauteur fixe de l'en-tête
    const rowHeights: string[] = [`${headerHeight}px`]; // Commencer par l'en-tête

    // Ajouter la hauteur de chaque feature row
    this.featureRows.forEach(featureRow => {
      const height = this.calculateFeatureRowHeight(featureRow);
      rowHeights.push(`${height}px`);
    });

    // Appliquer les hauteurs via CSS custom property
    const gridTemplateRows = rowHeights.join(' ');
    gridElement.style.setProperty('--grid-row-heights', gridTemplateRows);

    if (this.DEBUG_MODE) {
      console.log('Grid row heights:', gridTemplateRows);
    }
  }

  protected getUserStoriesStartingOnDay(featureRow: FeatureRow, day: TimelineDay): UserStoryBar[] {
    const dayIndex = this.timelineDays.findIndex(d => d.date.toDateString() === day.date.toDateString());
    return featureRow.userStories.filter(us => us.startDayIndex === dayIndex);
  }

  // Méthode pour obtenir le WorkItem d'une User Story (pour le tooltip)
  protected getUserStoryWorkItem(userStory: UserStoryBar): WorkItem {
    return userStory.userStory;
  }

  // Méthode pour extraire le nom du sprint depuis l'IterationPath
  protected getFeatureSprintName(iterationPath: string): string {
    if (!iterationPath) return 'Non défini';

    // L'IterationPath a généralement le format: "Project\Sprint 1" ou "Project\Area\Sprint 1"
    // On prend la dernière partie après le dernier backslash
    const parts = iterationPath.split('\\');
    return parts[parts.length - 1] || 'Non défini';
  }

  protected getUserStoryContinuousStyle(userStory: UserStoryBar): any {
    const baseWidth = userStory.durationDays * 120 - 8;
    const minWidth = 112;
    const width = Math.max(baseWidth, minWidth);
    const topPosition = 10 + (userStory.row * 47); // Augmenté pour accommoder la nouvelle hauteur

    return {
      'position': 'absolute',
      'left': '4px',
      'width': `${width}px`,
      'top': `${topPosition}px`,
      'height': '42px', // Augmenté pour accommoder 3 lignes
      'background-color': userStory.color,
      'z-index': '10',
      'border-radius': '4px',
      'border': '1px solid rgba(0, 0, 0, 0.1)',
      'min-width': `${minWidth}px`
    };
  }

  /**
   * Calcule la hauteur nécessaire pour une ligne de feature, en s'assurant que toutes les User Stories sont visibles.
   * Cette méthode doit être robuste pour éviter les chevauchements visuels.
   *
   */
  private calculateFeatureRowHeight(featureRow: FeatureRow): number {
    const usBarHeight = 42; // Hauteur réelle d'une barre de User Story (augmentée pour 3 lignes)
    const usLaneHeight = 47; // Hauteur allouée par "lane" de User Story (inclut la barre et l'espacement)
    const usInitialTopOffset = 10; // Décalage initial du haut de la cellule pour la première US
    const usBottomPadding = 10; // Espacement sous la dernière US (nouvelle constante pour clarté)

    // Hauteur minimale pour la ligne si pas d'User Stories ou si les US sont très courtes
    const minRowHeight = 150; // Valeur synchronisée avec $min-row-height dans _variables.scss

    let requiredHeightForUserStoriesArea = 0; // Hauteur nécessaire pour la section des US, depuis le HAUT de la cellule (incluant usInitialTopOffset)

    if (featureRow.userStories.length > 0) {
      // Trouver l'indice de la ligne la plus basse occupée par une User Story
      const maxRowIndex = Math.max(...featureRow.userStories.map(us => us.row));
      // Calculer la position verticale la plus basse atteinte par le bas d'une User Story
      // = (offset initial) + (position de la ligne la plus basse * hauteur d'une lane) + (hauteur de la barre d'US)
      const lowestUsBottomPosition = usInitialTopOffset + (maxRowIndex * usLaneHeight) + usBarHeight;
      // Ajouter le padding du bas pour la section des User Stories
      requiredHeightForUserStoriesArea = lowestUsBottomPosition + usBottomPadding;
    }

    // La hauteur finale de la ligne est le maximum entre la hauteur minimale de la ligne
    // (qui inclut déjà l'espace pour les infos de la feature et son padding)
    // et la hauteur calculée nécessaire pour afficher toutes les User Stories.
    // Cela garantit que toutes les US sont visibles et que la ligne suivante ne chevauche pas les US inférieures.
    return Math.max(minRowHeight, requiredHeightForUserStoriesArea);
  }



  protected getFeatureCellStyle(featureRow: FeatureRow): any {
    // Plus besoin de définir la hauteur ici, CSS Grid s'en charge
    return {
      'border-left-color': featureRow.color
    };
  }

  private getDayName(date: Date): string {
    return date.toLocaleDateString('fr-FR', { weekday: 'short' });
  }

  private formatDate(date: Date): string {
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
  }

  private isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  protected get isTodayVisible(): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today >= this.config.startDate && today <= this.config.endDate;
  }

  protected scrollToToday(): void {
    if (!this.isTodayVisible) {
      return;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayIndex = this.timelineDays.findIndex(day => {
      const dayDate = new Date(day.date);
      dayDate.setHours(0, 0, 0, 0);
      return dayDate.getTime() === today.getTime();
    });

    if (todayIndex === -1) {
      return;
    }

    const dayPosition = (todayIndex + 1) * this.config.dayWidth;
    const containerWidth = this.unifiedGrid?.nativeElement?.clientWidth || 800;
    const scrollPosition = Math.max(0, dayPosition - (containerWidth / 2) + (this.config.dayWidth / 2));

    if (this.unifiedGrid?.nativeElement) {
      this.unifiedGrid.nativeElement.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  }

}
