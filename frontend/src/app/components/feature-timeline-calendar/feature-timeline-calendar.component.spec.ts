import { TestBed, ComponentFixture } from '@angular/core/testing';

import { FeatureTimelineCalendarComponent } from './feature-timeline-calendar.component';
import { Sprint } from '../../interfaces/sprint';
import { WorkItem } from '../../interfaces/work-item';

/**
 * Tests unitaires pour FeatureTimelineCalendarComponent
 *
 * COUVERTURE MÉTIER :
 * - Construction de la timeline de planification
 * - Regroupement hiérarchique des features et user stories
 * - Génération des jours ouvrables pour la planification
 * - Logique de positionnement des éléments sur la timeline
 *
 * JUSTIFICATION :
 * Ces tests couvrent la logique métier complexe de planification :
 * - Construction de la timeline avec les bonnes règles métier (jours ouvrables)
 * - Regroupement des user stories sous leurs features parentes
 * - Calculs de positionnement pour l'affichage Gantt
 */

describe('FeatureTimelineCalendarComponent', () => {
  let component: FeatureTimelineCalendarComponent;
  let fixture: ComponentFixture<FeatureTimelineCalendarComponent>;

  const mockSprint: Sprint = {
    id: 'sprint-1',
    name: 'Sprint 1',
    path: 'Project\\Sprint 1',
    attributes: {
      startDate: '2024-01-01T00:00:00Z',
      finishDate: '2024-01-14T23:59:59Z',
      timeFrame: 'current'
    },
    url: 'http://test.com/sprint1'
  };

  const mockWorkItems: WorkItem[] = [
    {
      id: 1,
      rev: 1,
      fields: {
        'System.Id': 1,
        'System.Title': 'Authentification',
        'System.WorkItemType': 'Feature',
        'System.State': 'Active',
        'System.CreatedDate': '2024-01-01T00:00:00Z',
        'System.IterationPath': 'Project\\Sprint 1'
      },
      url: 'http://test.com/workitem1'
    },
    {
      id: 2,
      rev: 1,
      fields: {
        'System.Id': 2,
        'System.Title': 'Login utilisateur',
        'System.WorkItemType': 'User Story',
        'System.State': 'New',
        'System.Parent': 1,
        'System.CreatedDate': '2024-01-01T00:00:00Z',
        'System.IterationPath': 'Project\\Sprint 1',
        'System.AssignedTo': {
          displayName: 'John Doe',
          uniqueName: '<EMAIL>'
        },
        'Microsoft.VSTS.Scheduling.StartDate': '2024-01-02T00:00:00Z',
        'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-05T00:00:00Z'
      },
      url: 'http://test.com/workitem2'
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureTimelineCalendarComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(FeatureTimelineCalendarComponent);
    component = fixture.componentInstance;
  });

  /**
   * PERTINENCE : Validation que le composant calendrier se construit
   * correctement avec les données de planification
   */
  it('devrait construire la timeline avec les données du sprint', () => {
    component.sprint = mockSprint;
    component.workItems = mockWorkItems;

    component.ngOnChanges({
      sprint: {
        currentValue: mockSprint,
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true
      },
      workItems: {
        currentValue: mockWorkItems,
        previousValue: [],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    // Vérification que la timeline est construite
    expect(component['timelineDays'].length).toBeGreaterThan(0);
    expect(component['featureRows'].length).toBe(1);
  });

  /**
   * PERTINENCE : Logique métier de regroupement des user stories
   * sous leurs features parentes pour l'affichage hiérarchique
   */
  it('devrait regrouper les user stories sous leurs features parentes', () => {
    component.sprint = mockSprint;
    component.workItems = mockWorkItems;
    component['buildTimeline']();

    const featureRow = component['featureRows'][0];
    expect(featureRow.feature.fields['System.WorkItemType']).toBe('Feature');
    expect(featureRow.userStories.length).toBe(1);
    expect(featureRow.userStories[0].userStory.fields['System.Parent']).toBe(1);
  });

  /**
   * PERTINENCE : Génération des jours ouvrables uniquement (lundi-vendredi)
   * pour la planification, logique métier importante
   */
  it('devrait générer uniquement les jours ouvrables pour la planification', () => {
    component.sprint = mockSprint;
    component.workItems = mockWorkItems;
    component['buildTimeline']();

    const weekendDays = component['timelineDays'].filter(day => {
      const dayOfWeek = day.date.getDay();
      return dayOfWeek === 0 || dayOfWeek === 6; // Dimanche ou Samedi
    });

    expect(weekendDays.length).toBe(0);
  });
});
