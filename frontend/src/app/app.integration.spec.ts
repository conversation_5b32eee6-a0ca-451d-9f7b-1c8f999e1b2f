import { TestBed, ComponentFixture } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError, NEVER } from 'rxjs';

import { AppComponent } from './app.component';
import { PlanifyDataService } from './services/planify-data.service';

/**
 * Tests d'intégration pour l'application Planify
 *
 * COUVERTURE MÉTIER :
 * - Workflow complet de planification (de la sélection à l'affichage)
 * - États d'interface selon le contexte utilisateur
 * - Gestion d'erreurs avec possibilité de retry
 * - Intégration visuelle des composants
 *
 * JUSTIFICATION :
 * Ces tests d'intégration valident le workflow complet de l'utilisateur :
 * - Navigation entre les différents états de l'application
 * - Affichage conditionnel selon les données disponibles
 * - Gestion d'erreurs avec feedback utilisateur approprié
 */

describe('Planify - Tests d\'Intégration', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockService: jasmine.SpyObj<PlanifyDataService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PlanifyDataService', ['getSprints', 'getSprintFeatures']);

    await TestBed.configureTestingModule({
      imports: [AppComponent, HttpClientTestingModule, FormsModule],
      providers: [{ provide: PlanifyDataService, useValue: spy }]
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    mockService = TestBed.inject(PlanifyDataService) as jasmine.SpyObj<PlanifyDataService>;

    mockService.getSprints.and.returnValue(of([]));
    mockService.getSprintFeatures.and.returnValue(of([]));
  });

  /**
   * PERTINENCE : Validation de l'affichage des différents états de l'application
   * selon le contexte (chargement, erreur, bienvenue, calendrier)
   */
  it('devrait afficher l\'état de chargement pendant la récupération des données', () => {
    // Simuler l'état de chargement en cours avec un Observable qui ne se complète jamais
    mockService.getSprints.and.returnValue(NEVER);

    // Déclencher le chargement
    component.ngOnInit();
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const loading = compiled.querySelector('.loading-state');
    expect(loading).toBeTruthy();
    expect(loading?.textContent).toContain('Chargement...');
  });

  /**
   * PERTINENCE : Gestion d'erreur visible pour l'utilisateur avec possibilité
   * de retry via le bouton rafraîchir
   */
  it('devrait afficher les erreurs avec option de retry', () => {
    // Simuler une erreur de chargement
    mockService.getSprints.and.returnValue(throwError(() => new Error('Impossible de charger les sprints')));

    // Déclencher le chargement qui va échouer
    component.ngOnInit();
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const error = compiled.querySelector('.error-state');
    const refreshButton = compiled.querySelector('.refresh-button');

    expect(error).toBeTruthy();
    expect(error?.textContent).toContain('Impossible de charger les sprints');
    expect(refreshButton).toBeTruthy();
  });

  /**
   * PERTINENCE : État d'accueil quand aucun sprint n'est sélectionné,
   * guide l'utilisateur vers l'action suivante
   */
  it('devrait afficher le message d\'accueil quand aucun sprint n\'est sélectionné', () => {
    (component as any).isLoading = false;
    (component as any).errorMessage = null;
    (component as any).selectedSprint = null;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const welcome = compiled.querySelector('.welcome-state');
    expect(welcome).toBeTruthy();
    expect(welcome?.textContent).toContain('Bienvenue dans Planify');
  });
});
