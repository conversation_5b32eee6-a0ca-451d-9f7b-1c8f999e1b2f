export interface WorkItem {
  id: number;
  rev: number;
  fields: {
    'System.Id': number;
    'System.Title': string;
    'System.State': string;
    'System.WorkItemType': 'Feature' | 'User Story' | string;
    'System.CreatedDate': string;
    'System.IterationPath'?: string;
    'System.Parent'?: number;
    'System.AssignedTo'?: {
      displayName: string;
      uniqueName: string;
    };
    'Microsoft.VSTS.Scheduling.StartDate'?: string;
    'Microsoft.VSTS.Scheduling.FinishDate'?: string;
    'Microsoft.VSTS.Scheduling.Effort'?: number;
  };
  url: string;
}
