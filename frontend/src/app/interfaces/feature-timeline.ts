import { WorkItem } from './work-item';

export interface TimelineDay {
  date: Date;
  dayName: string;
  dayNumber: string;
  isToday: boolean;
}

export interface FeatureRow {
  feature: WorkItem;
  userStories: UserStoryBar[];
  color: string;
}

export interface UserStoryBar {
  userStory: WorkItem;
  startDate: Date;
  endDate: Date;
  startDayIndex: number;
  durationDays: number;
  assignedTo: string;
  title: string;
  state: string;
  color: string;
  row: number; // Pour éviter les chevauchements dans la même feature
}

export interface TimelineConfig {
  dayWidth: number;
  rowHeight: number;
  featureColumnWidth: number;
  startDate: Date;
  endDate: Date;
  totalDays: number;
}
