import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Sprint } from '../interfaces/sprint';
import { WorkItem } from '../interfaces/work-item';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PlanifyDataService {
  private readonly baseUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Récupère la liste de tous les sprints disponibles
   */
  getSprints(): Observable<Sprint[]> {
    return this.http.get<Sprint[]>(`${this.baseUrl}/sprints`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Récupère les features et user stories d'un sprint spécifique
   * @param sprintPath Le chemin du sprint
   */
  getSprintFeatures(sprintPath: string): Observable<WorkItem[]> {
    const encodedPath = encodeURIComponent(sprintPath);
    return this.http.get<WorkItem[]>(`${this.baseUrl}/sprints/${encodedPath}/features`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Gestion centralisée des erreurs HTTP
   * @param error L'erreur HTTP reçue
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur inconnue s\'est produite';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client (réseau, etc.)
      errorMessage = `Erreur de connexion: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      const statusMessages: Record<number, string> = {
        0: 'Impossible de contacter le serveur. Vérifiez votre connexion.',
        401: 'Accès non autorisé. Vérifiez vos identifiants.',
        403: 'Accès interdit.',
        404: 'Ressource non trouvée.',
        500: 'Erreur interne du serveur.',
        503: 'Service temporairement indisponible.'
      };

      errorMessage = statusMessages[error.status] ||
                    `Erreur ${error.status}: ${error.message || 'Erreur inconnue'}`;
    }

    // Log détaillé uniquement en mode debug
    if (environment.debug) {
      console.error('Erreur HTTP:', {
        status: error.status,
        message: error.message,
        url: error.url,
        error: error.error
      });
    }

    return throwError(() => new Error(errorMessage));
  }
}
