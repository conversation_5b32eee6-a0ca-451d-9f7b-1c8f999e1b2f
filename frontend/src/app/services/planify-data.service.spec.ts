import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { throwError } from 'rxjs';

import { PlanifyDataService } from './planify-data.service';
import { Sprint } from '../interfaces/sprint';
import { WorkItem } from '../interfaces/work-item';
import { environment } from '../../environments/environment';

/**
 * Tests unitaires pour PlanifyDataService
 * 
 * COUVERTURE MÉTIER :
 * - Communication API pour récupération des sprints et work items
 * - Gestion d'erreurs réseau et serveur (critique pour l'UX)
 * - Validation des données reçues de l'API
 * 
 * JUSTIFICATION :
 * Ces tests couvrent la couche de données, essentielle pour la fiabilité
 * de l'application de planification. La gestion d'erreur est critique
 * car l'utilisateur doit être informé si les données ne sont pas disponibles.
 */

describe('PlanifyDataService', () => {
  let service: PlanifyDataService;
  let httpMock: HttpTestingController;

  const mockSprints: Sprint[] = [
    {
      id: 'sprint-1',
      name: 'Sprint 1',
      path: 'Project\\Sprint 1',
      attributes: {
        startDate: '2024-01-01T00:00:00Z',
        finishDate: '2024-01-14T23:59:59Z',
        timeFrame: 'current'
      },
      url: 'http://test.com/sprint1'
    }
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PlanifyDataService]
    });
    service = TestBed.inject(PlanifyDataService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  /**
   * PERTINENCE : Valide la récupération des sprints, fonctionnalité centrale
   * de l'application pour la sélection et planification
   */
  it('devrait récupérer les sprints disponibles pour la planification', () => {
    service.getSprints().subscribe(sprints => {
      expect(sprints).toEqual(mockSprints);
      expect(sprints[0].name).toBe('Sprint 1');
      expect(sprints[0].attributes.timeFrame).toBe('current');
    });

    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSprints);
  });

  /**
   * PERTINENCE : Gestion d'erreur critique pour l'UX - l'utilisateur doit
   * être informé si les données de planification ne sont pas disponibles
   */
  it('devrait gérer les erreurs de récupération des sprints', () => {
    service.getSprints().subscribe({
      next: () => fail('Erreur attendue'),
      error: (error) => {
        expect(error).toBeTruthy();
        expect(error.message).toBeTruthy();
      }
    });

    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints`);
    req.flush('Erreur serveur', { status: 500, statusText: 'Server Error' });
  });

  /**
   * PERTINENCE : Récupération des features/user stories d'un sprint,
   * cœur de la logique de planification et affichage timeline
   */
  it('devrait récupérer les features et user stories d\'un sprint sélectionné', () => {
    const mockFeatures: WorkItem[] = [
      {
        id: 1,
        rev: 1,
        fields: {
          'System.Id': 1,
          'System.Title': 'Authentification utilisateur',
          'System.WorkItemType': 'Feature',
          'System.State': 'Active',
          'System.CreatedDate': '2024-01-01T00:00:00Z',
          'System.IterationPath': 'Project\\Sprint 1'
        },
        url: 'http://test.com/workitem1'
      },
      {
        id: 2,
        rev: 1,
        fields: {
          'System.Id': 2,
          'System.Title': 'Login utilisateur',
          'System.WorkItemType': 'User Story',
          'System.State': 'New',
          'System.Parent': 1,
          'System.CreatedDate': '2024-01-01T00:00:00Z',
          'System.IterationPath': 'Project\\Sprint 1',
          'Microsoft.VSTS.Scheduling.StartDate': '2024-01-02T00:00:00Z',
          'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-05T00:00:00Z'
        },
        url: 'http://test.com/workitem2'
      }
    ];

    const sprintPath = 'Project\\Sprint 1';
    service.getSprintFeatures(sprintPath).subscribe(features => {
      expect(features.length).toBe(2);
      expect(features[0].fields['System.WorkItemType']).toBe('Feature');
      expect(features[1].fields['System.WorkItemType']).toBe('User Story');
      expect(features[1].fields['System.Parent']).toBe(1);
    });

    const encodedPath = encodeURIComponent(sprintPath);
    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints/${encodedPath}/features`);
    req.flush(mockFeatures);
  });
});
