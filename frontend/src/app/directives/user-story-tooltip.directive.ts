import {
  Directive,
  Input,
  ElementRef,
  HostListener,
  ViewContainerRef,
  ComponentRef,
  OnDestroy
} from '@angular/core';
import { UserStoryTooltipComponent } from '../components/user-story-tooltip/user-story-tooltip.component';
import { WorkItem } from '../interfaces/work-item';

@Directive({
  selector: '[appUserStoryTooltip]',
  standalone: true
})
export class UserStoryTooltipDirective implements OnDestroy {
  @Input('appUserStoryTooltip') userStory: WorkItem | null = null;

  private tooltipComponent: ComponentRef<UserStoryTooltipComponent> | null = null;
  private showTimeout: number | null = null;
  private hideTimeout: number | null = null;

  constructor(
    private elementRef: ElementRef,
    private viewContainerRef: ViewContainerRef
  ) {}

  @HostListener('mouseenter', ['$event'])
  onMouseEnter(event: MouseEvent): void {
    // Annuler le timeout de masquage s'il existe
    if (this.hideTimeout) {
      window.clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    // D<PERSON>lai avant affichage pour éviter les tooltips intempestifs
    this.showTimeout = window.setTimeout(() => {
      this.showTooltip(event);
    }, 300);
  }

  @HostListener('mouseleave')
  onMouseLeave(): void {
    // Annuler le timeout d'affichage s'il existe
    if (this.showTimeout) {
      window.clearTimeout(this.showTimeout);
      this.showTimeout = null;
    }

    // Délai avant masquage pour permettre de passer sur le tooltip
    this.hideTimeout = window.setTimeout(() => {
      this.hideTooltip();
    }, 100);
  }

  private showTooltip(event: MouseEvent): void {
    if (!this.userStory || this.tooltipComponent) {
      return;
    }

    // Créer le composant tooltip
    this.tooltipComponent = this.viewContainerRef.createComponent(UserStoryTooltipComponent);
    this.tooltipComponent.instance.userStory = this.userStory;

    // Positionner le tooltip
    const tooltipElement = this.tooltipComponent.location.nativeElement;
    document.body.appendChild(tooltipElement);

    // Calculer la position
    const rect = this.elementRef.nativeElement.getBoundingClientRect();
    const tooltipRect = tooltipElement.getBoundingClientRect();

    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 10;

    // Ajustements pour éviter que le tooltip sorte de l'écran
    const padding = 10;

    // Ajustement horizontal
    if (left < padding) {
      left = padding;
    } else if (left + tooltipRect.width > window.innerWidth - padding) {
      left = window.innerWidth - tooltipRect.width - padding;
    }

    // Ajustement vertical - si pas assez de place en haut, afficher en bas
    if (top < padding) {
      top = rect.bottom + 10;
      // Inverser la flèche du tooltip
      tooltipElement.classList.add('tooltip-bottom');
    }

    tooltipElement.style.position = 'fixed';
    tooltipElement.style.left = `${left}px`;
    tooltipElement.style.top = `${top}px`;
    tooltipElement.style.zIndex = '1000';

    // Ajouter les event listeners pour maintenir le tooltip visible
    tooltipElement.addEventListener('mouseenter', () => {
      if (this.hideTimeout) {
        window.clearTimeout(this.hideTimeout);
        this.hideTimeout = null;
      }
    });

    tooltipElement.addEventListener('mouseleave', () => {
      this.hideTimeout = window.setTimeout(() => {
        this.hideTooltip();
      }, 100);
    });
  }

  private hideTooltip(): void {
    if (this.tooltipComponent) {
      const tooltipElement = this.tooltipComponent.location.nativeElement;
      if (tooltipElement.parentNode) {
        tooltipElement.parentNode.removeChild(tooltipElement);
      }
      this.tooltipComponent.destroy();
      this.tooltipComponent = null;
    }
  }

  ngOnDestroy(): void {
    // Nettoyer les timeouts
    if (this.showTimeout) {
      window.clearTimeout(this.showTimeout);
    }
    if (this.hideTimeout) {
      window.clearTimeout(this.hideTimeout);
    }

    // Masquer le tooltip
    this.hideTooltip();
  }
}
