// Import des variables globales
@use '../styles/variables' as vars;

// Interface principale
.planify-app {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: vars.$font-family;
  overflow: hidden;
  position: relative;

  * {
    font-family: vars.$font-family;
  }

  // Le composant calendrier doit prendre l'espace disponible
  app-feature-timeline-calendar {
    flex: 0 0 auto; // Ne pas utiliser flex: 1 pour éviter les conflits de hauteur
    overflow: hidden;
  }

  .app-header {
    background: white;
    border-bottom: 2px solid vars.$border-color;
    padding: 15px 25px;
    // Hauteur exacte : padding (15px*2) + border (2px) + contenu (~58px) = 90px
    height: 90px;
    min-height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
    flex-shrink: 0;
    box-sizing: border-box; // Important : inclure padding et border dans la hauteur

    .app-title {
      font-size: 2.2em;
      font-weight: 700;
      color: #333;
      margin: 0;
      letter-spacing: -0.5px;
      font-family: vars.$font-family;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .search-field-inline {
        display: flex;
        align-items: center;
        gap: 10px;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9em;
          font-family: vars.$font-family;
          white-space: nowrap;
        }

        .search-input-container {
          position: relative;
          display: flex;
          align-items: center;

          .search-input {
            min-width: 220px;
            padding: 8px 12px;
            padding-right: 35px; // Space for clear button
            font-size: 0.9em;
            font-family: vars.$font-family;
            border: 2px solid vars.$border-color;
            border-radius: 6px;
            background: white;
            transition: all 0.2s ease;
            height: 36px; // Match sprint selector height
            box-sizing: border-box;

            &:focus {
              outline: none;
              border-color: vars.$primary-color;
              box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
            }

            &:hover {
              border-color: #c6c8ca;
            }

            &::placeholder {
              color: #999;
              font-style: italic;
            }
          }

          .clear-search-btn {
            position: absolute;
            right: 40px; // Position to the left of search button
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 14px;
            padding: 2px;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 1;

            &:hover {
              background: #f0f0f0;
              color: #333;
            }

            &:active {
              background: #e0e0e0;
            }
          }

          .search-btn {
            position: absolute;
            right: 8px; // Far right position
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            .search-icon {
              width: 16px;
              height: 16px;
              stroke-width: 1.5;
            }

            &:hover {
              background: #f0f0f0;
              color: #000;
            }

            &:active {
              background: #e0e0e0;
            }
          }
        }
      }

      .sprint-selector-inline {
        display: flex;
        align-items: center;
        gap: 10px;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9em;
          font-family: vars.$font-family;
          white-space: nowrap;
        }

        select {
          min-width: 200px;
          padding: 8px 12px;
          font-size: 0.9em;
          font-family: vars.$font-family;
          border: 2px solid vars.$border-color;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          transition: all 0.2s ease;
          height: 36px; // Match search input height
          box-sizing: border-box;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }

          &:hover {
            border-color: #c6c8ca;
          }
        }
      }
    }
  }
}

// États de l'application
.loading-state, .error-state, .welcome-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  font-family: vars.$font-family;
}

.loading-state {
  flex-direction: column;
  gap: 15px;
  color: vars.$secondary-color;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid vars.$light-gray;
    border-top: 4px solid vars.$primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  span {
    font-size: 1.1em;
    font-weight: 500;
    font-family: vars.$font-family;
  }
}

.error-state {
  flex-direction: column;
  gap: 15px;
  color: vars.$error-color;
  background: #f8d7da;
  border-radius: 8px;
  margin: 20px;

  .error-icon {
    font-size: 2em;
  }

  span {
    font-size: 1.1em;
    font-weight: 500;
    font-family: vars.$font-family;
  }

  .refresh-button {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: vars.$primary-color;
    color: white;
    border: none;
    border-radius: 6px;
    font-family: vars.$font-family;
    font-size: 0.95em;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    align-self: center;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #106ebe;
    }

    &:active {
      background-color: #005a9e;
    }
  }
}

.welcome-state {
  .welcome-content {
    text-align: center;
    max-width: 500px;

    h2 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.8em;
      font-weight: 600;
      font-family: vars.$font-family;
    }

    p {
      color: vars.$secondary-color;
      font-size: 1.1em;
      line-height: 1.5;
      font-family: vars.$font-family;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
