import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Sprint} from './interfaces/sprint';
import {WorkItem} from './interfaces/work-item';
import {FeatureTimelineCalendarComponent} from './components/feature-timeline-calendar/feature-timeline-calendar.component';
import {PlanifyDataService} from './services/planify-data.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, FormsModule, FeatureTimelineCalendarComponent],

  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  protected sprints: Sprint[] = [];
  protected isLoading = true;
  protected errorMessage: string | null = null;
  protected selectedSprintPath: string = '';
  protected selectedSprintFeatures: WorkItem[] = [];
  protected selectedSprint: Sprint | null = null;
  protected searchTerm: string = '';
  private allSprintFeatures: WorkItem[] = []; // Stockage des données non filtrées

  constructor(private planifyDataService: PlanifyDataService) {}

  ngOnInit(): void {
    this.fetchSprints();
  }

  protected fetchSprints(): void {
    this.isLoading = true;
    this.errorMessage = null;

    this.planifyDataService.getSprints().subscribe({
      next: (responseSprints) => {
        this.sprints = responseSprints;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Erreur lors de la récupération des sprints: ${error.message}`;
        this.isLoading = false;
      }
    });
  }

  onSprintChange(): void {
    this.selectedSprintFeatures = [];
    this.allSprintFeatures = [];
    this.selectedSprint = null;
    this.searchTerm = ''; // Reset search when changing sprint

    if (!this.selectedSprintPath) return;

    // Trouver le sprint sélectionné
    this.selectedSprint = this.sprints.find(s => s.path === this.selectedSprintPath) || null;

    this.isLoading = true;

    this.planifyDataService.getSprintFeatures(this.selectedSprintPath).subscribe({
      next: (items) => {
        this.allSprintFeatures = items;
        this.applySearchFilter();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Erreur lors de la récupération des features/US: ${error.message}`;
        this.isLoading = false;
      }
    });
  }

  protected refreshPage(): void {
    window.location.reload();
  }

  performSearch(): void {
    this.applySearchFilter();
  }

  private applySearchFilter(): void {
    if (!this.searchTerm.trim()) {
      // Aucun terme de recherche : afficher toutes les features
      this.selectedSprintFeatures = this.allSprintFeatures;
      return;
    }

    const searchLower = this.searchTerm.toLowerCase().trim();

    // Filtrer les Features qui correspondent au terme de recherche
    const filteredFeatures = this.allSprintFeatures.filter(item => {
      if (item.fields['System.WorkItemType'] !== 'Feature') {
        return true; // Garder les User Stories (elles seront filtrées par leur Feature parent)
      }

      const title = item.fields['System.Title']?.toLowerCase() || '';
      const id = item.id.toString();

      // Recherche dans le titre (correspondance partielle) ou ID (correspondance exacte/partielle)
      return title.includes(searchLower) || id.includes(searchLower);
    });

    // Récupérer les IDs des Features qui correspondent
    const matchingFeatureIds = filteredFeatures
      .filter(item => item.fields['System.WorkItemType'] === 'Feature')
      .map(feature => feature.id);

    // Filtrer pour inclure les Features correspondantes ET leurs User Stories enfants
    this.selectedSprintFeatures = this.allSprintFeatures.filter(item => {
      if (item.fields['System.WorkItemType'] === 'Feature') {
        return matchingFeatureIds.includes(item.id);
      }

      // Pour les User Stories, inclure si leur parent est dans les Features correspondantes
      const parentId = item.fields['System.Parent'];
      return parentId && matchingFeatureIds.includes(parentId);
    });
  }

}
