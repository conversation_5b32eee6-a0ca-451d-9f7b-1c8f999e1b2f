<!-- Interface principale avec en-tête -->
<div class="planify-app">
  <!-- En-tête principal -->
  <div class="app-header">
    <h1 class="app-title">Planify</h1>

    <div class="header-controls">
      <div class="search-field-inline" *ngIf="selectedSprint">
        <label for="feature-search">Rechercher</label>
        <div class="search-input-container">
          <input
            id="feature-search"
            type="text"
            [(ngModel)]="searchTerm"
            (keydown.enter)="performSearch()"
            placeholder="Titre ou ID de feature..."
            class="search-input">
          <button
            *ngIf="searchTerm"
            type="button"
            class="clear-search-btn"
            (click)="searchTerm = ''; performSearch()"
            title="Effacer la recherche">
            ✕
          </button>
          <button
            type="button"
            class="search-btn"
            (click)="performSearch()"
            title="Rechercher">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
      </div>

      <div class="sprint-selector-inline">
        <label for="sprint-select">Itération</label>
        <select id="sprint-select" [(ngModel)]="selectedSprintPath" (change)="onSprintChange()">
          <option value="">-- Sélectionner --</option>
          <ng-container *ngFor="let sprint of sprints">
            <option *ngIf="sprint.attributes.timeFrame !== 'past'" [value]="sprint.path">
              {{ sprint.name }}
            </option>
          </ng-container>
        </select>
      </div>
    </div>
  </div>

  <!-- Messages d'état -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="loading-spinner"></div>
    <span>Chargement...</span>
  </div>

  <div *ngIf="errorMessage" class="error-state">
    <span class="error-icon">⚠</span>
    <span>{{ errorMessage }}</span>
    <button class="refresh-button" (click)="refreshPage()" type="button">
      Actualiser
    </button>
  </div>

  <!-- Planning -->
  <app-feature-timeline-calendar
    *ngIf="!isLoading && !errorMessage && selectedSprint"
    [sprint]="selectedSprint"
    [workItems]="selectedSprintFeatures">
  </app-feature-timeline-calendar>

  <!-- Message d'accueil -->
  <div *ngIf="!isLoading && !errorMessage && !selectedSprint" class="welcome-state">
    <div class="welcome-content">
      <h2>Bienvenue dans Planify</h2>
      <p>Sélectionnez une itération pour afficher le planning des features et user stories.</p>
    </div>
  </div>
</div>
