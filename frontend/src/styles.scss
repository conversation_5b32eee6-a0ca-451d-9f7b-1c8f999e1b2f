/* Variables globales */
@use 'styles/variables' as vars;

/* Styles globaux pour Planify */

// Reset global
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --header-height: #{vars.$header-height};
  --footer-height: #{vars.$footer-height};
}

body, html {
  height: 100%;
  font-family: vars.$font-family;
  font-size: vars.$font-size-base;
  color: vars.$text-dark;
  background: vars.$background-white;
  overflow: hidden; // Empêcher tout scroll global
}

// Classes utilitaires globales
.text-muted {
  color: vars.$text-muted;
}

.text-small {
  font-size: vars.$font-size-small;
}

.font-semibold {
  font-weight: vars.$font-weight-semibold;
}

.font-bold {
  font-weight: vars.$font-weight-bold;
}
