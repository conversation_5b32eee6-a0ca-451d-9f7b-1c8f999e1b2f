import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config'; // Fichier de configuration de l'application
import { AppComponent } from './app/app.component';
import {environment} from './environments/environment';
import {loadModule} from './environments/environmentLoader';
import {enableProdMode} from '@angular/core'; // Composant racine


if (environment.production) {
  enableProdMode();
}

loadModule(environment)
  .then(() => {
    bootstrapApplication(AppComponent, appConfig)
      .catch((err) => console.error(err));
  });
