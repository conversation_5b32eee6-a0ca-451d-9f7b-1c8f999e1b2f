import {enableProdMode} from '@angular/core';
import {platformBrowserDynamic} from '@angular/platform-browser-dynamic';
import {environment} from './environment';
import {APP_BASE_HREF} from "@angular/common";

/**
 * Used to load at runtime what is in environment.json in environment object.
 *
 * @param moduleEnvironment
 */
export async function loadModule(moduleEnvironment: any): Promise<void> {
  // eslint-disable-next-line
  const settings = await new Promise<any>((resolve) => {
    const xmlhttp: XMLHttpRequest = new XMLHttpRequest();
    const method = 'GET';
    const url = './settings/environment.json';
    xmlhttp.open(method, url, true);
    xmlhttp.setRequestHeader('Cache-Control', 'must-revalidate, public');
    xmlhttp.onload = () => {
      if (xmlhttp.status === 200) {
        resolve(JSON.parse(xmlhttp.responseText));
      } else {
        resolve(environment);
      }
    };
    xmlhttp.send();
  });
  merge(moduleEnvironment, settings, environment.production);
  merge(environment, settings, environment.production);

  if (moduleEnvironment.production) {
    console.log('Production mode enabled');
    enableProdMode();
  }
  const baseHref = environment.baseHref || '/';
  platformBrowserDynamic([{ provide: APP_BASE_HREF, useValue: baseHref }])
}

// @ts-ignore
function merge(internalSettings, externalSettings, active): void {
  if (active) {
    for (const property of Object.keys(externalSettings)) {
      if (typeof externalSettings[property] === 'object') {
        if (internalSettings[property] === undefined || internalSettings[property] === null) {
          internalSettings[property] = {};
        }
        merge(internalSettings[property], externalSettings[property], active);
      } else {
        internalSettings[property] = externalSettings[property];
      }
    }
  }
}

