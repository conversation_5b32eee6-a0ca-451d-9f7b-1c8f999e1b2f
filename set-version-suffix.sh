#!/bin/bash

if [ -z ${CI+x} ]; then
  workdir="$PWD"
  if [ ! -d ".git" ]; then
    if [ "$#" -ne 1 ]; then
      echo "Working directory is not a git repository, use argument to give git repository folder path"
      exit 1
    fi
    workdir=$1
  fi
else
  workdir=$CI_PROJECT_DIR
fi

if [ -z ${NO_VERSION_SUFFIX+x} ]; then
  if [ -z ${CI+x} ]; then
    if [ -z ${IT_VERSION_SUFFIX+x} ]; then
      versionHead=$(cd $workdir && git rev-parse --abbrev-ref HEAD)
      if [[ $versionHead == HEAD ]]; then
        gitTag=$(cd $workdir && git describe --tags --abbrev=0)
        versionSuffix=$(cd $workdir && echo $gitTag | sed 's/^v[0-9]*\.[0-9]*\.[0-9]*-\(.*\)/\1/')
      else
        if [[ $versionHead == renovate/* ]]; then
          versionSuffix="renovate"
        else
          versionSuffix=$(echo "$versionHead" | sed s#/#-#g)
        fi
      fi
    else
      versionSuffix=${IT_VERSION_SUFFIX}
    fi
  else
    if [[ $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME == renovate/* ]]; then
      versionSuffix="bot-renovate-${CI_MERGE_REQUEST_IID}"
    else
      if [ -z ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME+x} ]; then
        if [[ -z "${CI_COMMIT_TAG}" ]]; then
          versionSuffix=${CI_COMMIT_BRANCH}
        else
          versionSuffix=$(echo "$CI_COMMIT_TAG" |cut -d - -f 2-)
        fi
      else
        if [[ $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME == mr-to-$CI_MERGE_REQUEST_TARGET_BRANCH_NAME ]]; then
          versionSuffix=mr-to-${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}
        else
          sourceBranch=$(echo "$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME" | sed s#/#-#g)
          if [[ $sourceBranch =~ ^[0-9a-zA-Z-]+$ ]]; then
            versionSuffix=$sourceBranch
          else
            echo "Unexpected merge request source branch name '${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}'"
            exit 1
          fi
        fi
      fi
    fi
  fi
else
  versionSuffix=""
fi

echo $versionSuffix > ${workdir}/_buildVersionSuffix
