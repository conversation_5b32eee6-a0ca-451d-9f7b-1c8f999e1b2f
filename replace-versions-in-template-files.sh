#!/bin/bash

if [ -z ${CI+x} ]; then
  if [ "$#" -eq 1 ]; then
    workdir=$1
  else
    workdir="$PWD"
  fi
else
  workdir=$CI_PROJECT_DIR
fi

versionsFile=$workdir/_versions
if [ ! -f "$versionsFile" ]; then
  if [ "$#" -ne 1 ]; then
    echo "Must give path to the project folder"
    exit 1
  fi
  workdir=$1
  versionsFile=$workdir/_versions
  if [ ! -f "$versionsFile" ]; then
    echo "Cannot find versions file '$versionsFile'"
    exit 2
  fi
fi

versions=$(cat $versionsFile | grep .version || true)

templateFiles=$(find $workdir -name "*.tpl")
for templateFile in $templateFiles
do
  destinationFile=${templateFile%.tpl}
  echo "Replacing versions in template file '$templateFile', generating '$destinationFile'"
  cp $templateFile $destinationFile
  for line in $versions
  do
    IFS='=' read key version <<< $line
    sed -i "s/@$key@/$version/" $destinationFile
  done
done
