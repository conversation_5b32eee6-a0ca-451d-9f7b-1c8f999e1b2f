import os
import base64
import requests
import urllib3
from flask import Flask, jsonify
from flask_cors import CORS
from functools import wraps
from datetime import datetime, UTC
from test_data import TEST_SPRINTS, TEST_WORK_ITEMS

# Configuration SSL - À MODIFIER EN PRODUCTION
DISABLE_SSL_WARNINGS = os.environ.get("DISABLE_SSL_WARNINGS", "false").lower() == "true"
if DISABLE_SSL_WARNINGS:
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    print("Vérification SSL désactivée")

AZURE_DEVOPS_SERVER_URL = os.environ.get("AZURE_DEVOPS_SERVER_URL")
AZURE_DEVOPS_ORG = os.environ.get("AZURE_DEVOPS_ORG")
AZURE_DEVOPS_PROJECT = os.environ.get("AZURE_DEVOPS_PROJECT")
AZURE_DEVOPS_TEAM = os.environ.get("AZURE_DEVOPS_TEAM")
AZURE_DEVOPS_PAT = os.environ.get("AZURE_DEVOPS_PAT")
SPRINT_PATH_PREFIX = os.environ.get("SPRINT_PATH_PREFIX", "")

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

# --- Décorateur pour vérification du PAT --- sert à s'assurer que le PAT est configuré avant d'exécuter les routes API
def require_pat(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        if not AZURE_DEVOPS_PAT:
            return jsonify({"error": "Le PAT n'est pas configuré."}), 500
        return f(*args, **kwargs)
    return wrapper

# --- CONSTANTES ---
# Champs optimisés - seulement ceux utilisés par l'interface de Planify
WORK_ITEM_FIELDS = [
    "System.Id",
    "System.Title",
    "System.State",
    "System.WorkItemType",
    "System.IterationPath",
    "System.CreatedDate",
    "Microsoft.VSTS.Scheduling.StartDate",
    "Microsoft.VSTS.Scheduling.FinishDate",
    "System.AssignedTo",
    "Microsoft.VSTS.Scheduling.Effort"
]

# --- Utilitaires ---
def _get_auth_header():
    return {
        'Authorization': f'Basic {base64.b64encode(f":{AZURE_DEVOPS_PAT}".encode("ascii")).decode("ascii")}'
    }

def _make_request(method: str, url: str, **kwargs):
    """Fonction centralisée pour les requêtes HTTP avec gestion d'erreurs"""
    try:
        verify_ssl = not DISABLE_SSL_WARNINGS
        response = requests.request(method, url, verify=verify_ssl, **kwargs)
        response.raise_for_status()
        return response
    except requests.exceptions.RequestException as e:
        app.logger.error(f"Erreur requête {method} {url}: {e}")
        raise

def _get_work_item_ids(query: str):
    url = f"{AZURE_DEVOPS_SERVER_URL}/{AZURE_DEVOPS_ORG}/{AZURE_DEVOPS_PROJECT}/_apis/wit/wiql?api-version=7.1"
    response = _make_request('POST', url, headers={**_get_auth_header(), 'Content-Type': 'application/json'}, json={"query": query})
    return [str(item["id"]) for item in response.json().get("workItems", [])]

def _get_work_item_details(ids: list):
    if not ids:
        return []

    url = f"{AZURE_DEVOPS_SERVER_URL}/{AZURE_DEVOPS_ORG}/_apis/wit/workitemsbatch?api-version=7.1"
    body = {"ids": ids, "fields": WORK_ITEM_FIELDS}
    response = _make_request('POST', url, headers={**_get_auth_header(), 'Content-Type': 'application/json'}, json=body)
    return response.json().get("value", [])

def _execute_wiql_query(query: str):
    ids = _get_work_item_ids(query)
    return _get_work_item_details(ids) if ids else []

# --- Routes API ---
# Permet de vérifier que l'API est accessible
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(UTC).isoformat()
    })

# Permet de récupérer tous les sprints
@app.route('/api/sprints', methods=['GET'])
def get_sprints():
    try:
        # Mode test si pas de PAT configuré
        if not AZURE_DEVOPS_PAT:
            app.logger.info("Mode test activé - utilisation des données de démonstration")
            return jsonify(TEST_SPRINTS)

        url = f"{AZURE_DEVOPS_SERVER_URL}/{AZURE_DEVOPS_ORG}/{AZURE_DEVOPS_PROJECT}/{AZURE_DEVOPS_TEAM}/_apis/work/teamsettings/iterations?api-version=7.1"
        response = _make_request('GET', url, headers=_get_auth_header())
        sprints = response.json().get("value", [])

        # Filtrage par SPRINT_PATH_PREFIX (logique existante)
        if SPRINT_PATH_PREFIX:
            sprints = [s for s in sprints if s.get("path", "").startswith(SPRINT_PATH_PREFIX)]

        # Filtrage spécifique pour l'équipe Invoice : exclure les sprints commençant par "ERP -"
        if AZURE_DEVOPS_TEAM == "Invoice":
            sprints = [s for s in sprints if not s.get("name", "").startswith("ERP -")]
            app.logger.info(f"Filtrage Invoice appliqué - {len(sprints)} sprints après exclusion des 'ERP -'")

        return jsonify(sprints)
    except Exception as e:
        app.logger.error(f"Erreur dans get_sprints: {e}")
        return jsonify({"error": str(e)}), 500

# Permet de récupérer les features et user stories d'un sprint spécifique
@app.route('/api/sprints/<path:iteration_path>/features', methods=['GET'])
def get_features_for_sprint(iteration_path):
    try:
        # Mode test si pas de PAT configuré
        if not AZURE_DEVOPS_PAT:
            app.logger.info(f"Mode test activé - filtrage des work items pour {iteration_path}")
            filtered_items = []
            for item in TEST_WORK_ITEMS:
                item_iteration = item.get('fields', {}).get('System.IterationPath', '')
                if item_iteration == iteration_path:
                    filtered_items.append(item)
            app.logger.info(f"Trouvé {len(filtered_items)} work items pour {iteration_path}")
            return jsonify(filtered_items)

        normalized_path = iteration_path.replace("/", "\\")
        # 1. Récupérer tous les work items du sprint
        query = (
            f"SELECT [System.Id] FROM WorkItems "
            f"WHERE ([System.WorkItemType] = 'Feature' OR [System.WorkItemType] = 'User Story' OR [System.WorkItemType] = 'Task') "
            f"AND [System.TeamProject] = '{AZURE_DEVOPS_PROJECT}' "
            f"AND [System.IterationPath] = '{normalized_path}'"
        )
        ids = _get_work_item_ids(query)
        if not ids:
            return jsonify([])
        # 2. Récupérer les détails des work items
        work_items = _get_work_item_details(ids)

        # 3. Récupérer les liens parent/enfant pour ces work items
        url_wiql = f"{AZURE_DEVOPS_SERVER_URL}/{AZURE_DEVOPS_ORG}/{AZURE_DEVOPS_PROJECT}/_apis/wit/wiql?api-version=7.1"
        wiql_links = {
            "query": (
                f"SELECT [System.Id], [System.Links.LinkType], [System.WorkItemType] FROM WorkItemLinks "
                f"WHERE ([Source].[System.Id] IN ({','.join(ids)})) "
                f"AND ([System.Links.LinkType] = 'System.LinkTypes.Hierarchy-Reverse') "
                f"ORDER BY [System.Id] mode(MustContain)"
            )
        }
        resp_links = _make_request('POST', url_wiql, headers={**_get_auth_header(), 'Content-Type': 'application/json'}, json=wiql_links)
        links_json = resp_links.json()
        # Correction de robustesse : s'assurer que workItemRelations est une liste
        links = links_json.get("workItemRelations")
        if not isinstance(links, list):
            app.logger.warning(f"Aucun lien parent/enfant trouvé ou structure inattendue pour les work items du sprint {iteration_path}. Réponse: {links_json}")
            links = []

        # 4. Construire un mapping enfant -> parent
        child_to_parent = {}
        for link in links:
            # Vérification robuste des clés
            if isinstance(link, dict) and 'target' in link and 'source' in link:
                source = link.get('source')
                target = link.get('target')
                if isinstance(source, dict) and isinstance(target, dict):
                    child_to_parent[str(source.get('id'))] = target.get('id')

        # 5. Injecter le parent dans chaque work item User Story
        for wi in work_items:
            if wi.get('fields', {}).get('System.WorkItemType') == 'User Story':
                wi['fields']['System.Parent'] = child_to_parent.get(str(wi['id']))

        # 6. Identifier les Features parentes manquantes
        existing_feature_ids = {wi['id'] for wi in work_items if wi.get('fields', {}).get('System.WorkItemType') == 'Feature'}
        parent_feature_ids = {parent_id for parent_id in child_to_parent.values() if parent_id and parent_id not in existing_feature_ids}

        # 7. Récupérer les Features parentes manquantes
        if parent_feature_ids:
            app.logger.info(f"Récupération de {len(parent_feature_ids)} Features parentes manquantes: {list(parent_feature_ids)}")

            parent_features = _get_work_item_details(list(parent_feature_ids))
            work_items.extend(parent_features)

            app.logger.info(f"Ajouté {len(parent_features)} Features parentes à l'itération {iteration_path}")

        app.logger.info(f"Total final pour {iteration_path}: {len(work_items)} work items "
                    f"({len([wi for wi in work_items if wi.get('fields', {}).get('System.WorkItemType') == 'Feature'])} Features, "
                    f"{len([wi for wi in work_items if wi.get('fields', {}).get('System.WorkItemType') == 'User Story'])} User Stories)")

        return jsonify(work_items)
    except Exception as e:
        app.logger.error(f"Erreur dans get_features_for_sprint: {e}")
        return jsonify({"error": str(e)}), 500

# Permet de récupérer les "Backlogs" (Features) non planifiés"
@app.route('/api/backlog/features', methods=['GET'])
@require_pat
def get_backlog_features():
    try:
        query = (
            f"SELECT [System.Id] FROM WorkItems "
            f"WHERE [System.WorkItemType] = 'Feature' "
            f"AND [System.TeamProject] = '{AZURE_DEVOPS_PROJECT}' "
            f"AND [System.State] NOT IN ('Resolved', 'Closed', 'Removed')"
        )
        return jsonify(_execute_wiql_query(query))
    except Exception as e:
        app.logger.error(f"Erreur dans get_backlog_features: {e}")
        return jsonify({"error": str(e)}), 500

# Permet de récupérer les user stories associées à une feature spécifique
@app.route('/api/features/<int:feature_id>/userstories', methods=['GET'])
@require_pat
def get_user_stories_for_feature(feature_id):
    try:
        url = f"{AZURE_DEVOPS_SERVER_URL}/{AZURE_DEVOPS_ORG}/_apis/wit/workItems/{feature_id}?$expand=relations&api-version=7.1"
        response = _make_request('GET', url, headers=_get_auth_header())
        relations = response.json().get("relations", [])
        child_ids = [rel.get("url", "").split("/")[-1] for rel in relations if rel.get("rel") == "System.LinkTypes.Hierarchy-Forward" and rel.get("url", "").split("/")[-1].isdigit()]
        return jsonify(_get_work_item_details(child_ids) if child_ids else [])
    except Exception as e:
        app.logger.error(f"Erreur dans get_user_stories_for_feature: {e}")
        return jsonify({"error": str(e)}), 500

# --- Point d'entrée ---
if __name__ == '__main__':
    app.run(debug=True, port=5000)
