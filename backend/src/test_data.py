"""
Jeu de données amélioré pour Planify (Mise à jour pour 2025-07-01)
"""
from datetime import datetime, timedelta

# Date de référence pour la génération des données.
# Utiliser datetime.now() rend les données dynamiques par rapport au jour d'exécution.
TODAY = datetime.now()

# Nouveaux membres de l'équipe
PERSON_SOPHIE = {"displayName": "<PERSON>", "uniqueName": "<EMAIL>"}
PERSON_LUC = {"displayName": "<PERSON> Moreau", "uniqueName": "<EMAIL>"}
PERSON_JEAN = {"displayName": "<PERSON>", "uniqueName": "<EMAIL>"}
PERSON_MARIE = {"displayName": "<PERSON>", "uniqueName": "<EMAIL>"}
PERSON_PIERRE = {"displayName": "<PERSON>", "uniqueName": "<EMAIL>"}

# Données de test pour les sprints avec des dates plus réalistes
TEST_SPRINTS = [
    {
        "id": "sprint-0",
        "name": "Sprint 0 - Initialisation",
        "path": "MyProject\\Sprint 0",
        "attributes": {
            # Sprint de deux semaines qui s'est terminé la semaine dernière
            "startDate": (TODAY - timedelta(days=18)).isoformat(),
            "finishDate": (TODAY - timedelta(days=4)).isoformat(),
            "timeFrame": "past"
        },
        "url": "https://dev.azure.com/test/sprint0"
    },
    {
        "id": "sprint-1",
        "name": "Sprint 1 - Développement Core",
        "path": "MyProject\\Sprint 1",
        "attributes": {
            # Sprint actuel de deux semaines
            "startDate": (TODAY - timedelta(days=3)).isoformat(),
            "finishDate": (TODAY + timedelta(days=11)).isoformat(),
            "timeFrame": "current"
        },
        "url": "https://dev.azure.com/test/sprint1"
    },
    {
        "id": "sprint-2",
        "name": "Sprint 2 - Interface Utilisateur",
        "path": "MyProject\\Sprint 2",
        "attributes": {
            # Prochain sprint, commençant après le sprint actuel
            "startDate": (TODAY + timedelta(days=12)).isoformat(),
            "finishDate": (TODAY + timedelta(days=26)).isoformat(),
            "timeFrame": "future"
        },
        "url": "https://dev.azure.com/test/sprint2"
    },
    {
        "id": "sprint-erp-1",
        "name": "ERP - Module Facturation",
        "path": "MyProject\\ERP - Module Facturation",
        "attributes": {
            # Sprint ERP qui devrait être filtré pour l'équipe Invoice
            "startDate": (TODAY + timedelta(days=5)).isoformat(),
            "finishDate": (TODAY + timedelta(days=19)).isoformat(),
            "timeFrame": "future"
        },
        "url": "https://dev.azure.com/test/sprint-erp-1"
    },
    {
        "id": "sprint-erp-2",
        "name": "ERP - Gestion des stocks",
        "path": "MyProject\\ERP - Gestion des stocks",
        "attributes": {
            # Autre sprint ERP qui devrait être filtré pour l'équipe Invoice
            "startDate": (TODAY + timedelta(days=27)).isoformat(),
            "finishDate": (TODAY + timedelta(days=41)).isoformat(),
            "timeFrame": "future"
        },
        "url": "https://dev.azure.com/test/sprint-erp-2"
    }
]

# Données de test pour les work items avec des statuts et dates variés
TEST_WORK_ITEMS = [
    # === FEATURES existantes ===
    {
        "id": 1001, "rev": 2, "fields": {
        "System.Id": 1001,
        "System.Title": "[IN_CTCFR - OUTBOUND] Authentification utilisateur",
        "System.State": "Active",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.CreatedDate": (TODAY - timedelta(days=20)).isoformat(),
        "System.Description": "Mise en place du système d'authentification complet.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=15)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 20,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/1001"
    },
    {
        "id": 1002, "rev": 1, "fields": {
        "System.Id": 1002,
        "System.Title": "[LOGS-RATIO] Système de logs et métriques",
        "System.State": "New",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.CreatedDate": (TODAY - timedelta(days=8)).isoformat(),
        "System.Description": "Implémentation du logging et de la collecte de métriques.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=12)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=26)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 15,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/1002"
    },

    # === NOUVELLES FEATURES Sprint 0 ===
    {
        "id": 1010, "rev": 1, "fields": {
        "System.Id": 1010,
        "System.Title": "[INIT-ENV] Initialisation de l’environnement",
        "System.State": "Closed",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 0",
        "System.CreatedDate": (TODAY - timedelta(days=19)).isoformat(),
        "System.Description": "Mise en place des outils et du dépôt Git.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=18)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY - timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 8,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/1010"
    },
    {
        "id": 1011, "rev": 1, "fields": {
        "System.Id": 1011,
        "System.Title": "[DOC-BASE] Documentation de base",
        "System.State": "Closed",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 0",
        "System.CreatedDate": (TODAY - timedelta(days=18)).isoformat(),
        "System.Description": "Rédaction de la documentation initiale du projet.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=17)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY - timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/1011"
    },

    # === NOUVELLES FEATURES Sprint 1 ===
    {
        "id": 1012, "rev": 1, "fields": {
        "System.Id": 1012,
        "System.Title": "[API-CORE] Développement de l’API principale",
        "System.State": "Active",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.CreatedDate": (TODAY - timedelta(days=4)).isoformat(),
        "System.Description": "Création des endpoints principaux de l’API.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 12,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/1012"
    },
    {
        "id": 1013, "rev": 1, "fields": {
        "System.Id": 1013,
        "System.Title": "[NOTIF] Système de notifications",
        "System.State": "New",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.CreatedDate": (TODAY - timedelta(days=2)).isoformat(),
        "System.Description": "Ajout d’un système de notifications pour les utilisateurs.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 7,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/1013"
    },
    {
        "id": 1016, "rev": 1, "fields": {
        "System.Id": 1016,
        "System.Title": "[DATA-SYNC] Synchronisation des données",
        "System.State": "Active",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.CreatedDate": (TODAY - timedelta(days=1)).isoformat(),
        "System.Description": "Mise en place d'un système de synchronisation des données en temps réel.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 10,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/1016"
    },
    {
        "id": 1017, "rev": 1, "fields": {
        "System.Id": 1017,
        "System.Title": "[SEARCH] Moteur de recherche avancée",
        "System.State": "New",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.CreatedDate": TODAY.isoformat(),
        "System.Description": "Développement d'un moteur de recherche avec filtres et tri avancés.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 8,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/1017"
    },

    # === NOUVELLES FEATURES Sprint 2 ===
    {
        "id": 1014, "rev": 1, "fields": {
        "System.Id": 1014,
        "System.Title": "[UI-REFRESH] Refonte graphique de l’interface",
        "System.State": "New",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.CreatedDate": (TODAY + timedelta(days=12)).isoformat(),
        "System.Description": "Modernisation de l’UI pour une meilleure expérience.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=13)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=22)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 10,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/1014"
    },
    {
        "id": 1015, "rev": 1, "fields": {
        "System.Id": 1015,
        "System.Title": "[EXPORT] Export des données au format CSV",
        "System.State": "New",
        "System.WorkItemType": "Feature",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.CreatedDate": (TODAY + timedelta(days=13)).isoformat(),
        "System.Description": "Permettre l’export des rapports au format CSV.",
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=15)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=25)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 6,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/1015"
    },

    # === USER STORIES pour la Feature 1001 (AUTH-LOGIN) ===
    {
        "id": 2001, "rev": 3, "fields": {
        "System.Id": 2001,
        "System.Title": "[IN_CTCFR - OUTBOUND] [CON] Page de connexion",
        "System.State": "Closed",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY - timedelta(days=17)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=15)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY - timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2001"
    },
    {
        "id": 2002, "rev": 2, "fields": {
        "System.Id": 2002,
        "System.Title": "[IN_CTCFR - OUTBOUND] [VAL] Validation des formulaires",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY - timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2002"
    },
    {
        "id": 2003, "rev": 1, "fields": {
        "System.Id": 2003,
        "System.Title": "[IN_CTCFR - OUTBOUND] [JWT] Gestion des sessions JWT",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY - timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 8,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2003"
    },
    # === USER STORIES supplémentaires pour la Feature 1001 (AUTH-LOGIN) ===
    {
        "id": 2006, "rev": 1, "fields": {
        "System.Id": 2006,
        "System.Title": "Gestion des mots de passe oubliés",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2006"
    },
    {
        "id": 2007, "rev": 1, "fields": {
        "System.Id": 2007,
        "System.Title": "Blocage après 5 tentatives échouées",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2007"
    },
    {
        "id": 2008, "rev": 1, "fields": {
        "System.Id": 2008,
        "System.Title": "Historique des connexions",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2008"
    },
    {
        "id": 2009, "rev": 1, "fields": {
        "System.Id": 2009,
        "System.Title": "Authentification à double facteur",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2009"
    },
    {
        "id": 2040, "rev": 1, "fields": {
        "System.Id": 2040,
        "System.Title": "Interface de déconnexion sécurisée",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2040"
    },
    {
        "id": 2041, "rev": 1, "fields": {
        "System.Id": 2041,
        "System.Title": "Gestion des rôles utilisateurs",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2041"
    },
    {
        "id": 2042, "rev": 1, "fields": {
        "System.Id": 2042,
        "System.Title": "Audit des connexions utilisateurs",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2042"
    },
    {
        "id": 2043, "rev": 1, "fields": {
        "System.Id": 2043,
        "System.Title": "Intégration avec Active Directory",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2043"
    },
    {
        "id": 2044, "rev": 1, "fields": {
        "System.Id": 2044,
        "System.Title": "Politique de mots de passe complexes",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2044"
    },

    # === USER STORIES pour la Feature 1012 (API-CORE) ===
    {
        "id": 2010, "rev": 1, "fields": {
        "System.Id": 2010,
        "System.Title": "Endpoint de création d'utilisateur",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY - timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY - timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2010"
    },
    {
        "id": 2011, "rev": 1, "fields": {
        "System.Id": 2011,
        "System.Title": "Endpoint de gestion des profils",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY - timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2011"
    },
    # === USER STORIES supplémentaires pour la Feature 1012 (API-CORE) ===
    {
        "id": 2014, "rev": 1, "fields": {
        "System.Id": 2014,
        "System.Title": "Endpoint de gestion des permissions",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2014"
    },
    {
        "id": 2015, "rev": 1, "fields": {
        "System.Id": 2015,
        "System.Title": "Validation et sanitisation des données",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2015"
    },
    {
        "id": 2050, "rev": 1, "fields": {
        "System.Id": 2050,
        "System.Title": "Endpoint de recherche d'utilisateurs",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2050"
    },
    {
        "id": 2051, "rev": 1, "fields": {
        "System.Id": 2051,
        "System.Title": "API de gestion des fichiers",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2051"
    },
    {
        "id": 2052, "rev": 1, "fields": {
        "System.Id": 2052,
        "System.Title": "Middleware de logging des requêtes",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2052"
    },
    {
        "id": 2053, "rev": 1, "fields": {
        "System.Id": 2053,
        "System.Title": "Gestion des erreurs et codes de retour",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2053"
    },
    {
        "id": 2054, "rev": 1, "fields": {
        "System.Id": 2054,
        "System.Title": "Documentation automatique des endpoints",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2054"
    },
    {
        "id": 2055, "rev": 1, "fields": {
        "System.Id": 2055,
        "System.Title": "Tests unitaires des endpoints",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2055"
    },
    {
        "id": 2056, "rev": 1, "fields": {
        "System.Id": 2056,
        "System.Title": "Cache Redis pour les requêtes fréquentes",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2056"
    },

    # === USER STORIES pour la Feature 1013 (NOTIF) ===
    {
        "id": 2012, "rev": 1, "fields": {
        "System.Id": 2012,
        "System.Title": "Interface de configuration des notifications",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY - timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2012"
    },
    {
        "id": 2013, "rev": 1, "fields": {
        "System.Id": 2013,
        "System.Title": "Service d'envoi de notifications push",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2013"
    },
    {
        "id": 2060, "rev": 1, "fields": {
        "System.Id": 2060,
        "System.Title": "Notifications par email",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2060"
    },
    {
        "id": 2061, "rev": 1, "fields": {
        "System.Id": 2061,
        "System.Title": "Notifications SMS",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2061"
    },
    {
        "id": 2062, "rev": 1, "fields": {
        "System.Id": 2062,
        "System.Title": "Historique des notifications envoyées",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2062"
    },
    {
        "id": 2063, "rev": 1, "fields": {
        "System.Id": 2063,
        "System.Title": "Templates de notifications personnalisables",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2063"
    },
    {
        "id": 2064, "rev": 1, "fields": {
        "System.Id": 2064,
        "System.Title": "Notifications en temps réel via WebSocket",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2064"
    },
    {
        "id": 2065, "rev": 1, "fields": {
        "System.Id": 2065,
        "System.Title": "Gestion des préférences de notification",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2065"
    },

    # === USER STORIES pour la Feature 1016 (DATA-SYNC) ===
    {
        "id": 2030, "rev": 1, "fields": {
        "System.Id": 2030,
        "System.Title": "Configuration des sources de données",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY - timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2030"
    },
    {
        "id": 2031, "rev": 1, "fields": {
        "System.Id": 2031,
        "System.Title": "Service de synchronisation en temps réel",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2031"
    },
    {
        "id": 2032, "rev": 1, "fields": {
        "System.Id": 2032,
        "System.Title": "Gestion des conflits de synchronisation",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2032"
    },
    {
        "id": 2070, "rev": 1, "fields": {
        "System.Id": 2070,
        "System.Title": "Synchronisation incrémentale des données",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2070"
    },
    {
        "id": 2071, "rev": 1, "fields": {
        "System.Id": 2071,
        "System.Title": "Monitoring de la synchronisation",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2071"
    },
    {
        "id": 2072, "rev": 1, "fields": {
        "System.Id": 2072,
        "System.Title": "Rollback automatique en cas d'erreur",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2072"
    },
    {
        "id": 2073, "rev": 1, "fields": {
        "System.Id": 2073,
        "System.Title": "Interface de configuration des règles de sync",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2073"
    },
    {
        "id": 2074, "rev": 1, "fields": {
        "System.Id": 2074,
        "System.Title": "Logs détaillés de synchronisation",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2074"
    },
    {
        "id": 2075, "rev": 1, "fields": {
        "System.Id": 2075,
        "System.Title": "Optimisation des performances de sync",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1016,
        "System.CreatedDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2075"
    },

    # === USER STORIES pour la Feature 1017 (SEARCH) ===
    {
        "id": 2033, "rev": 1, "fields": {
        "System.Id": 2033,
        "System.Title": "Interface de recherche avec filtres",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2033"
    },
    {
        "id": 2034, "rev": 1, "fields": {
        "System.Id": 2034,
        "System.Title": "Algorithme de recherche textuelle",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2034"
    },
    {
        "id": 2035, "rev": 1, "fields": {
        "System.Id": 2035,
        "System.Title": "Sauvegarde des recherches favorites",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2035"
    },
    {
        "id": 2080, "rev": 1, "fields": {
        "System.Id": 2080,
        "System.Title": "Recherche par mots-clés avec autocomplétion",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=3)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 4,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2080"
    },
    {
        "id": 2081, "rev": 1, "fields": {
        "System.Id": 2081,
        "System.Title": "Filtres avancés par date et catégorie",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=4)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2081"
    },
    {
        "id": 2082, "rev": 1, "fields": {
        "System.Id": 2082,
        "System.Title": "Tri des résultats par pertinence",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=5)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2082"
    },
    {
        "id": 2083, "rev": 1, "fields": {
        "System.Id": 2083,
        "System.Title": "Export des résultats de recherche",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=6)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2083"
    },
    {
        "id": 2084, "rev": 1, "fields": {
        "System.Id": 2084,
        "System.Title": "Historique des recherches utilisateur",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=7)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 2,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2084"
    },
    {
        "id": 2085, "rev": 1, "fields": {
        "System.Id": 2085,
        "System.Title": "Recherche géolocalisée",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=8)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2085"
    },
    {
        "id": 2086, "rev": 1, "fields": {
        "System.Id": 2086,
        "System.Title": "API de recherche pour applications tierces",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1017,
        "System.CreatedDate": (TODAY + timedelta(days=9)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=10)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=11)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2086"
    },

    # === USER STORIES pour la Feature 1002 (LOGS-RATIO), prévues pour le sprint 2 ===
    {
        "id": 2004, "rev": 1, "fields": {
        "System.Id": 2004,
        "System.Title": "Configuration des niveaux de logs",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1002,
        "System.CreatedDate": (TODAY - timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=12)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=18)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2004"
    },
    {
        "id": 2005, "rev": 1, "fields": {
        "System.Id": 2005,
        "System.Title": "Dashboard de visualisation des métriques",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1002,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=19)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=25)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 8,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 3
    }, "url": "https://dev.azure.com/test/workitem/2005"
    },

    # === USER STORIES pour la Feature 1014 (UI-REFRESH), prévues pour le sprint 2 ===
    {
        "id": 2020, "rev": 1, "fields": {
        "System.Id": 2020,
        "System.Title": "Refonte de la page d'accueil",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1014,
        "System.CreatedDate": (TODAY + timedelta(days=12)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=13)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=17)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2020"
    },
    {
        "id": 2021, "rev": 1, "fields": {
        "System.Id": 2021,
        "System.Title": "Nouveau système de navigation",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1014,
        "System.CreatedDate": (TODAY + timedelta(days=13)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=18)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=22)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 5,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2021"
    },

    # === USER STORIES pour la Feature 1015 (EXPORT), prévues pour le sprint 2 ===
    {
        "id": 2022, "rev": 1, "fields": {
        "System.Id": 2022,
        "System.Title": "Interface de sélection des données à exporter",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1015,
        "System.CreatedDate": (TODAY + timedelta(days=13)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=15)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=19)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_LUC,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2022"
    },
    {
        "id": 2023, "rev": 1, "fields": {
        "System.Id": 2023,
        "System.Title": "Génération et téléchargement du fichier CSV",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 2",
        "System.Parent": 1015,
        "System.CreatedDate": (TODAY + timedelta(days=14)).isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=20)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=25)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 3,
        "System.AssignedTo": PERSON_SOPHIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2023"
    },

    # === USER STORIES AVEC DATES IDENTIQUES (TEST DU FIX) ===
    {
        "id": 2090, "rev": 1, "fields": {
        "System.Id": 2090,
        "System.Title": "Hotfix - Correction critique",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1001,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=1)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2090"
    },
    {
        "id": 2091, "rev": 1, "fields": {
        "System.Id": 2091,
        "System.Title": "Validation rapide",
        "System.State": "New",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1012,
        "System.CreatedDate": TODAY.isoformat(),
        "Microsoft.VSTS.Scheduling.StartDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.FinishDate": (TODAY + timedelta(days=2)).isoformat(),
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_MARIE,
        "Microsoft.VSTS.Common.Priority": 2
    }, "url": "https://dev.azure.com/test/workitem/2091"
    },

    # === FORMAT PRODUCTION UTC (TEST FINAL) ===
    {
        "id": 2092, "rev": 1, "fields": {
        "System.Id": 2092,
        "System.Title": "Test format production UTC",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": "2025-06-11T16:11:16.26Z",
        "Microsoft.VSTS.Scheduling.StartDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.FinishDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_PIERRE,
        "Microsoft.VSTS.Common.Priority": 1
    }, "url": "https://dev.azure.com/test/workitem/2092"
    },
    {
        "id": 3000, "rev": 1, "fields": {
        "System.Id": 3000,
        "System.Title": "Rédaction feature - Test UTC exact",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": "2025-06-11T16:11:16.26Z",
        "Microsoft.VSTS.Scheduling.StartDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.FinishDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 2,
        "Microsoft.VSTS.Common.StateChangeDate": "2025-06-11T16:11:16.26Z",
        "Microsoft.VSTS.Common.ValueArea": "Business",
        "System.AreaPath": "Developpement\\SLFI",
        "System.BoardColumn": "Active",
        "System.BoardColumnDone": False,
        "System.ChangedDate": "2025-06-24T09:48:02.777Z",
        "System.CommentCount": 0,
        "System.Reason": "New",
        "System.Tags": "auto; STEP1",
        "System.TeamProject": "Developpement",
        "System.Description": "Rédaction feature- Description"
    }, "url": "https://seraimtfs20.itesoft.local/ItesoftCollection/91e1217c-0bc6-4782-9bac-38c0a7a35ac8/_apis/wit/workItems/3000"
    },
    {
        "id": 3000, "rev": 1, "fields": {
        "System.Id": 3000,
        "System.Title": "Rédaction feature - Test UTC exact",
        "System.State": "Active",
        "System.WorkItemType": "User Story",
        "System.IterationPath": "MyProject\\Sprint 1",
        "System.Parent": 1013,
        "System.CreatedDate": "2025-06-11T16:11:16.26Z",
        "Microsoft.VSTS.Scheduling.StartDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.FinishDate": "2025-06-25T00:00:00Z",
        "Microsoft.VSTS.Scheduling.Effort": 1,
        "System.AssignedTo": PERSON_JEAN,
        "Microsoft.VSTS.Common.Priority": 2,
        "Microsoft.VSTS.Common.StateChangeDate": "2025-06-11T16:11:16.26Z",
        "Microsoft.VSTS.Common.ValueArea": "Business",
        "System.AreaPath": "Developpement\\SLFI",
        "System.BoardColumn": "Active",
        "System.BoardColumnDone": False,
        "System.ChangedDate": "2025-06-24T09:48:02.777Z",
        "System.CommentCount": 0,
        "System.Reason": "New",
        "System.Tags": "auto; STEP1",
        "System.TeamProject": "Developpement",
        "System.Description": "Rédaction feature- Description"
    }, "url": "https://seraimtfs20.itesoft.local/ItesoftCollection/91e1217c-0bc6-4782-9bac-38c0a7a35ac8/_apis/wit/workItems/3000"
    },
]
