# Configuration Azure DevOps pour Planify
# Copiez ce fichier vers .env et remplissez les valeurs

# URL du serveur Azure DevOps
AZURE_DEVOPS_SERVER_URL=https://seraimtfs20.itesoft.local

# Organisation Azure DevOps
AZURE_DEVOPS_ORG=ItesoftCollection

# Projet Azure DevOps
AZURE_DEVOPS_PROJECT=SLIDevSandbox

# Équipe Azure DevOps
AZURE_DEVOPS_TEAM=SLIDevSandboxTeam

# Personal Access Token (PAT) Azure DevOps
# Permissions requises: Work Items (Read), Analytics (Read)
# ⚠️ NE JAMAIS VERSIONNER AVEC UNE VRAIE VALEUR
# Laissez vide ou commenté pour utiliser le mode test (données factices mais semblables à une production)
#AZURE_DEVOPS_PAT=your_pat_here

# Préfixe optionnel pour filtrer les sprints
SPRINT_PATH_PREFIX=PATH\TO\YOUR\SPRINTS

# Configuration SSL (DÉVELOPPEMENT UNIQUEMENT)
# Mettre à "true" pour désactiver les vérifications SSL
# ⚠️ NE JAMAIS UTILISER EN PRODUCTION !
DISABLE_SSL_WARNINGS=false