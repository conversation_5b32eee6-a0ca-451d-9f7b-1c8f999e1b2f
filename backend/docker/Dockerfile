#renovate: datasource=docker depName=itesoft/external/python3_11
ARG IMAGE_VERSION=25.7.1

FROM itesoft/external/python3_11:${IMAGE_VERSION}

ARG IMAGE_VERSION
ARG VERSION
ARG CI_COMMIT_SHA
ARG CI_JOB_STARTED_AT

LABEL org.opencontainers.image.authors="<EMAIL>" \
      org.opencontainers.image.title="Itesoft's planify backend" \
      org.opencontainers.image.description="Itesoft's planify backend based on python3_11" \
      org.opencontainers.image.base.name="itesoft/external/python3_11:${IMAGE_VERSION}" \
      org.opencontainers.image.vendor="ITESOFT" \
      org.opencontainers.image.version="$VERSION" \
      org.opencontainers.image.revision="$CI_COMMIT_SHA" \
      org.opencontainers.image.create="$CI_JOB_STARTED_AT"

# Set PYTHONPATH for Python imports to work
ENV PYTHONPATH="/app/src"

# Set the working directory
WORKDIR /app

# Copy dependency files first (for better Docker layer caching)
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install curl
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy application files
COPY src/ ./src/

# Expose the port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Command to run the application with Gunicorn
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "--timeout", "120", "src.app:app"]